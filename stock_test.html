<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票监控测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">A股实时监控测试</h1>
        
        <!-- 测试按钮 -->
        <div class="mb-6">
            <button id="testBtn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                生成测试数据
            </button>
            <span id="status" class="ml-4 text-gray-600"></span>
        </div>

        <!-- 市场指数 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2">上证指数</h3>
                <div class="text-2xl font-bold text-gray-900" id="sh-index">3424.23</div>
                <div class="text-sm text-red-600" id="sh-change">-24.22 (-0.70%)</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2">深证成指</h3>
                <div class="text-2xl font-bold text-gray-900" id="sz-index">10378.55</div>
                <div class="text-sm text-green-600" id="sz-change">+35.07 (+0.34%)</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2">创业板指</h3>
                <div class="text-2xl font-bold text-gray-900" id="cy-index">2124.34</div>
                <div class="text-sm text-green-600" id="cy-change">+9.91 (+0.47%)</div>
            </div>
        </div>

        <!-- 股票表格 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b">
                <h2 class="text-lg font-semibold">实时行情</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票代码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">当前价</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">涨跌幅</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">成交量</th>
                        </tr>
                    </thead>
                    <tbody id="stockTable" class="divide-y divide-gray-200">
                        <!-- 数据将在这里生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        console.log('脚本开始执行');

        // 测试数据
        const testStocks = [
            { code: '000001.SZ', name: '平安银行', price: 12.20, change: -1.69 },
            { code: '600000.SH', name: '浦发银行', price: 13.55, change: -0.51 },
            { code: '600519.SH', name: '贵州茅台', price: 1403.09, change: -1.19 },
            { code: '000858.SZ', name: '五粮液', price: 168.50, change: 2.35 },
            { code: '002415.SZ', name: '海康威视', price: 35.80, change: 1.25 }
        ];

        function generateRandomData() {
            console.log('生成随机数据');
            const tbody = document.getElementById('stockTable');
            tbody.innerHTML = '';

            testStocks.forEach(stock => {
                // 随机变化价格
                const randomChange = (Math.random() - 0.5) * 5;
                const newPrice = stock.price + randomChange;
                const changePct = (randomChange / stock.price) * 100;
                
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                
                const changeColor = changePct > 0 ? 'text-green-600' : changePct < 0 ? 'text-red-600' : 'text-gray-600';
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${stock.code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${stock.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold ${changeColor}">¥${newPrice.toFixed(2)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${changeColor}">
                        ${changePct > 0 ? '+' : ''}${changePct.toFixed(2)}%
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${(Math.random() * 1000000).toFixed(0)}
                    </td>
                `;
                
                tbody.appendChild(row);
            });

            // 更新指数
            updateIndices();
            
            document.getElementById('status').textContent = `数据已更新 - ${new Date().toLocaleTimeString()}`;
        }

        function updateIndices() {
            // 随机更新指数
            const shChange = (Math.random() - 0.5) * 100;
            const szChange = (Math.random() - 0.5) * 200;
            const cyChange = (Math.random() - 0.5) * 50;

            document.getElementById('sh-index').textContent = (3424.23 + shChange).toFixed(2);
            document.getElementById('sz-index').textContent = (10378.55 + szChange).toFixed(2);
            document.getElementById('cy-index').textContent = (2124.34 + cyChange).toFixed(2);

            const shPct = (shChange / 3424.23) * 100;
            const szPct = (szChange / 10378.55) * 100;
            const cyPct = (cyChange / 2124.34) * 100;

            document.getElementById('sh-change').textContent = 
                `${shChange > 0 ? '+' : ''}${shChange.toFixed(2)} (${shPct > 0 ? '+' : ''}${shPct.toFixed(2)}%)`;
            document.getElementById('sz-change').textContent = 
                `${szChange > 0 ? '+' : ''}${szChange.toFixed(2)} (${szPct > 0 ? '+' : ''}${szPct.toFixed(2)}%)`;
            document.getElementById('cy-change').textContent = 
                `${cyChange > 0 ? '+' : ''}${cyChange.toFixed(2)} (${cyPct > 0 ? '+' : ''}${cyPct.toFixed(2)}%)`;

            // 更新颜色
            document.getElementById('sh-change').className = `text-sm ${shPct > 0 ? 'text-green-600' : 'text-red-600'}`;
            document.getElementById('sz-change').className = `text-sm ${szPct > 0 ? 'text-green-600' : 'text-red-600'}`;
            document.getElementById('cy-change').className = `text-sm ${cyPct > 0 ? 'text-green-600' : 'text-red-600'}`;
        }

        // 事件监听
        document.getElementById('testBtn').addEventListener('click', function() {
            console.log('按钮被点击');
            generateRandomData();
        });

        // 页面加载完成后立即生成数据
        console.log('设置页面加载事件');
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOMContentLoaded 触发');
                generateRandomData();
                // 设置自动刷新
                setInterval(generateRandomData, 3000);
            });
        } else {
            console.log('页面已加载，立即执行');
            generateRandomData();
            // 设置自动刷新
            setInterval(generateRandomData, 3000);
        }

        console.log('脚本执行完成');
    </script>
</body>
</html>
