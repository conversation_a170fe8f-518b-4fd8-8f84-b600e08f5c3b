#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试xtdata库的导入和基本功能
"""

import sys
import os

# 将当前目录添加到Python路径中，这样可以导入xtdata模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_import():
    """测试导入xtdata模块"""
    try:
        print("正在尝试导入xtdata模块...")
        
        # 尝试导入xtdata模块
        import xtdata
        print("✓ xtdata模块导入成功！")
        
        # 检查模块的基本属性
        print(f"xtdata模块位置: {xtdata.__file__}")
        
        # 尝试获取一些基本信息
        print("正在测试基本连接...")
        
        # 检查是否有可用的函数
        available_functions = [attr for attr in dir(xtdata) if not attr.startswith('_')]
        print(f"可用函数数量: {len(available_functions)}")
        print("主要函数:", available_functions[:10])  # 显示前10个函数
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入xtdata失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False

def test_connection():
    """测试与MiniQMT的连接"""
    try:
        import xtdata
        
        print("\n正在测试与MiniQMT的连接...")
        
        # 尝试连接
        client = xtdata.get_client()
        if client and client.is_connected():
            print("✓ 成功连接到MiniQMT服务器")
            return True
        else:
            print("✗ 无法连接到MiniQMT服务器")
            print("请确保:")
            print("1. MiniQMT客户端已经启动")
            print("2. MiniQMT正在运行并监听默认端口")
            return False
            
    except Exception as e:
        import traceback
        print(f"✗ 连接测试失败: {e}")
        print("详细错误信息:")
        print(traceback.format_exc())
        print("这通常意味着:")
        print("1. MiniQMT客户端未启动")
        print("2. 网络连接问题")
        print("3. 端口被占用或配置错误")
        print("4. 还有其他模块的相对导入问题")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("XtData 库测试程序")
    print("=" * 50)
    
    # 测试导入
    import_success = test_import()
    
    if import_success:
        # 测试连接
        connection_success = test_connection()
        
        if connection_success:
            print("\n✓ 所有测试通过！xtdata库可以正常使用。")
        else:
            print("\n⚠ 导入成功，但连接失败。请检查MiniQMT客户端状态。")
    else:
        print("\n✗ 导入失败，请检查库文件完整性。")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
