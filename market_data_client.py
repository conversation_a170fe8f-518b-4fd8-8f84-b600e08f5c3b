# -*- coding: utf-8 -*-
"""
简化的市场数据客户端
专门用于获取实时行情和历史数据
"""

import xtdata
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional


class MarketDataClient:
    """市场数据客户端"""
    
    def __init__(self):
        self.connected = False
        
    def connect(self) -> bool:
        """连接到MiniQMT服务器"""
        try:
            result = xtdata.connect()
            self.connected = bool(result)
            return self.connected
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            xtdata.disconnect()
            self.connected = False
        except Exception:
            pass
    
    def get_stock_list(self, sector: str = '沪深A股') -> List[str]:
        """
        获取股票列表
        
        Args:
            sector: 板块名称，如'沪深A股'、'上证A股'、'深证A股'等
            
        Returns:
            股票代码列表
        """
        try:
            return xtdata.get_stock_list_in_sector(sector)
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def get_realtime_quote(self, stock_codes: List[str]) -> Dict:
        """
        获取实时行情
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            实时行情数据字典
        """
        try:
            fields = ['time', 'lastPrice', 'volume', 'turnover', 
                     'openPrice', 'highPrice', 'lowPrice', 'preClose', 'pctChg']
            
            data = xtdata.get_market_data(
                field_list=fields,
                stock_list=stock_codes,
                period='1m'
            )
            return data or {}
        except Exception as e:
            print(f"获取实时行情失败: {e}")
            return {}
    
    def get_historical_data(self, 
                          stock_codes: List[str], 
                          period: str = '1d',
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None,
                          count: int = 100) -> Dict[str, pd.DataFrame]:
        """
        获取历史数据
        
        Args:
            stock_codes: 股票代码列表
            period: 周期，如'1m', '5m', '15m', '30m', '1h', '1d'等
            start_date: 开始日期，格式'YYYYMMDD'
            end_date: 结束日期，格式'YYYYMMDD'
            count: 获取条数（当不指定日期时使用）
            
        Returns:
            {股票代码: DataFrame} 的字典
        """
        try:
            # 如果没有指定日期，使用count参数
            if not start_date or not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
                if period == '1d':
                    start_date = (datetime.now() - timedelta(days=count)).strftime('%Y%m%d')
                else:
                    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            
            fields = ['time', 'open', 'high', 'low', 'close', 'volume', 'turnover']
            
            result = {}
            for stock_code in stock_codes:
                try:
                    data = xtdata.get_market_data_ex(
                        field_list=fields,
                        stock_list=[stock_code],
                        period=period,
                        start_time=start_date,
                        end_time=end_date
                    )
                    
                    if data and stock_code in data:
                        df = pd.DataFrame(data[stock_code])
                        if not df.empty:
                            # 转换时间格式
                            if 'time' in df.columns:
                                df['time'] = pd.to_datetime(df['time'], unit='ms')
                            result[stock_code] = df
                except Exception as e:
                    print(f"获取 {stock_code} 历史数据失败: {e}")
                    
            return result
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return {}
    
    def get_tick_data(self, stock_codes: List[str]) -> Dict:
        """
        获取tick数据（逐笔数据）
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            tick数据字典
        """
        try:
            return xtdata.get_full_tick(stock_codes) or {}
        except Exception as e:
            print(f"获取tick数据失败: {e}")
            return {}
    
    def subscribe_quote(self, 
                       stock_codes: List[str], 
                       period: str = '1m',
                       callback=None):
        """
        订阅实时行情推送
        
        Args:
            stock_codes: 股票代码列表
            period: 周期
            callback: 回调函数
        """
        try:
            xtdata.subscribe_quote(
                stock_list=stock_codes,
                period=period,
                callback=callback
            )
        except Exception as e:
            print(f"订阅行情失败: {e}")
    
    def unsubscribe_quote(self, stock_codes: List[str], period: str = '1m'):
        """
        取消订阅
        
        Args:
            stock_codes: 股票代码列表
            period: 周期
        """
        try:
            xtdata.unsubscribe_quote(stock_codes, period)
        except Exception as e:
            print(f"取消订阅失败: {e}")


def demo():
    """使用示例"""
    # 创建客户端
    client = MarketDataClient()
    
    # 连接服务器
    if not client.connect():
        print("连接失败，请检查MiniQMT是否启动")
        return
    
    print("连接成功！")
    
    try:
        # 获取股票列表
        stocks = client.get_stock_list('沪深A股')
        test_stocks = stocks[:5] if stocks else ['000001.SZ', '000002.SZ', '600000.SH']
        print(f"测试股票: {test_stocks}")
        
        # 获取实时行情
        print("\n=== 实时行情 ===")
        realtime_data = client.get_realtime_quote(test_stocks)
        for stock, data in realtime_data.items():
            print(f"{stock}: 最新价={data.get('lastPrice', 'N/A')}, "
                  f"涨跌幅={data.get('pctChg', 'N/A')}%")
        
        # 获取历史数据
        print("\n=== 历史数据 ===")
        historical_data = client.get_historical_data(test_stocks[:2], period='1d', count=10)
        for stock, df in historical_data.items():
            print(f"\n{stock} 最近10天数据:")
            if not df.empty:
                print(df[['time', 'open', 'high', 'low', 'close', 'volume']].tail())
            else:
                print("无数据")
        
        # 获取tick数据
        print("\n=== Tick数据 ===")
        tick_data = client.get_tick_data(test_stocks[:2])
        for stock, data in tick_data.items():
            print(f"{stock}: {data}")
            
    finally:
        # 断开连接
        client.disconnect()
        print("\n已断开连接")


if __name__ == "__main__":
    demo()
