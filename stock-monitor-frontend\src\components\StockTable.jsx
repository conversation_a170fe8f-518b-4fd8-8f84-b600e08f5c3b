import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, X, Eye } from 'lucide-react'

const StockTable = ({ stocks, onSelectStock, onRemoveStock, loading }) => {
  const [prevPrices, setPrevPrices] = useState({})
  const [priceAnimations, setPriceAnimations] = useState({})

  // 监听价格变化并添加动画
  useEffect(() => {
    const newAnimations = {}
    
    stocks.forEach(stock => {
      const prevPrice = prevPrices[stock.code]
      if (prevPrice !== undefined && prevPrice !== stock.current) {
        newAnimations[stock.code] = stock.current > prevPrice ? 'up' : 'down'
      }
    })

    if (Object.keys(newAnimations).length > 0) {
      setPriceAnimations(newAnimations)
      
      // 清除动画
      setTimeout(() => {
        setPriceAnimations({})
      }, 500)
    }

    // 更新前一次价格
    const newPrevPrices = {}
    stocks.forEach(stock => {
      newPrevPrices[stock.code] = stock.current
    })
    setPrevPrices(newPrevPrices)
  }, [stocks])

  const formatNumber = (num) => {
    if (num >= 100000000) {
      return (num / 100000000).toFixed(1) + '亿'
    } else if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    }
    return num?.toLocaleString() || '0'
  }

  const getChangeColor = (change) => {
    if (change > 0) return 'text-stock-green'
    if (change < 0) return 'text-stock-red'
    return 'text-gray-500'
  }

  const getChangeBgColor = (change) => {
    if (change > 0) return 'bg-green-50'
    if (change < 0) return 'bg-red-50'
    return 'bg-gray-50'
  }

  if (loading && stocks.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">实时行情</h2>
        <p className="text-sm text-gray-500 mt-1">
          共 {stocks.length} 只股票 • 每5秒自动刷新
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                股票
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                当前价
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                涨跌
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                涨跌幅
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                成交量
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stocks.map((stock) => {
              const animation = priceAnimations[stock.code]
              const animationClass = animation === 'up' ? 'price-up' : animation === 'down' ? 'price-down' : ''
              
              return (
                <tr 
                  key={stock.code} 
                  className={`hover:bg-gray-50 transition-colors ${animationClass}`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {stock.code}
                      </div>
                      <div className="text-sm text-gray-500">
                        {stock.name}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${getChangeColor(stock.change)}`}>
                      ¥{stock.current?.toFixed(2)}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`flex items-center text-sm font-medium ${getChangeColor(stock.change)}`}>
                      {stock.change > 0 ? (
                        <TrendingUp className="h-4 w-4 mr-1" />
                      ) : stock.change < 0 ? (
                        <TrendingDown className="h-4 w-4 mr-1" />
                      ) : null}
                      {stock.change > 0 ? '+' : ''}{stock.change?.toFixed(2)}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getChangeBgColor(stock.change_pct)} ${getChangeColor(stock.change_pct)}`}>
                      {stock.change_pct > 0 ? '+' : ''}{stock.change_pct?.toFixed(2)}%
                    </span>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(stock.volume)}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {stock.time}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onSelectStock(stock)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="查看图表"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onRemoveStock(stock.code)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="移除"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {stocks.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-500">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">暂无股票数据</p>
            <p className="text-sm mt-2">请在搜索框中添加股票代码</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default StockTable
