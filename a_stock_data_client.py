# -*- coding: utf-8 -*-
"""
中国A股实时行情和历史数据获取客户端
基于免费API，无需安装额外客户端
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class AStockDataClient:
    """中国A股数据客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def get_stock_list(self) -> pd.DataFrame:
        """
        获取A股股票列表

        Returns:
            包含股票代码、名称、市场等信息的DataFrame
        """
        # 直接返回常用股票列表，避免API问题
        default_stocks = [
            {'stock_code': '000001.SZ', 'stock_name': '平安银行', 'simple_code': '000001'},
            {'stock_code': '000002.SZ', 'stock_name': '万科A', 'simple_code': '000002'},
            {'stock_code': '000858.SZ', 'stock_name': '五粮液', 'simple_code': '000858'},
            {'stock_code': '002415.SZ', 'stock_name': '海康威视', 'simple_code': '002415'},
            {'stock_code': '002594.SZ', 'stock_name': '比亚迪', 'simple_code': '002594'},
            {'stock_code': '300059.SZ', 'stock_name': '东方财富', 'simple_code': '300059'},
            {'stock_code': '600000.SH', 'stock_name': '浦发银行', 'simple_code': '600000'},
            {'stock_code': '600036.SH', 'stock_name': '招商银行', 'simple_code': '600036'},
            {'stock_code': '600276.SH', 'stock_name': '恒瑞医药', 'simple_code': '600276'},
            {'stock_code': '600519.SH', 'stock_name': '贵州茅台', 'simple_code': '600519'},
        ]
        return pd.DataFrame(default_stocks)
    
    def get_realtime_quote(self, stock_codes: List[str]) -> Dict:
        """
        获取实时行情数据
        
        Args:
            stock_codes: 股票代码列表，格式如['000001.SZ', '600000.SH']
            
        Returns:
            实时行情数据字典
        """
        try:
            # 转换股票代码格式为新浪财经格式
            sina_codes = []
            for code in stock_codes:
                if code.endswith('.SH'):
                    sina_codes.append('sh' + code.replace('.SH', ''))
                elif code.endswith('.SZ'):
                    sina_codes.append('sz' + code.replace('.SZ', ''))
            
            if not sina_codes:
                return {}
            
            # 新浪财经实时行情API
            url = f"http://hq.sinajs.cn/list={','.join(sina_codes)}"
            response = self.session.get(url, timeout=10)
            response.encoding = 'gbk'
            
            result = {}
            lines = response.text.strip().split('\n')
            
            for i, line in enumerate(lines):
                if i >= len(stock_codes):
                    break
                    
                if 'var hq_str_' in line:
                    # 解析数据
                    data_str = line.split('"')[1]
                    if data_str:
                        parts = data_str.split(',')
                        if len(parts) >= 32:
                            stock_code = stock_codes[i]
                            result[stock_code] = {
                                'name': parts[0],           # 股票名称
                                'open': float(parts[1]) if parts[1] else 0,      # 开盘价
                                'pre_close': float(parts[2]) if parts[2] else 0, # 昨收价
                                'current': float(parts[3]) if parts[3] else 0,   # 当前价
                                'high': float(parts[4]) if parts[4] else 0,      # 最高价
                                'low': float(parts[5]) if parts[5] else 0,       # 最低价
                                'volume': int(parts[8]) if parts[8] else 0,      # 成交量(股)
                                'turnover': float(parts[9]) if parts[9] else 0,  # 成交额(元)
                                'time': parts[30] + ' ' + parts[31],             # 时间
                                'change': float(parts[3]) - float(parts[2]) if parts[3] and parts[2] else 0,
                                'change_pct': ((float(parts[3]) - float(parts[2])) / float(parts[2]) * 100) if parts[3] and parts[2] and float(parts[2]) > 0 else 0
                            }
            
            return result
            
        except Exception as e:
            print(f"获取实时行情失败: {e}")
            return {}
    
    def get_historical_data(self, 
                          stock_code: str, 
                          period: str = 'daily',
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None,
                          count: int = 100) -> pd.DataFrame:
        """
        获取历史数据
        
        Args:
            stock_code: 股票代码，如'000001.SZ'
            period: 周期，支持'daily', 'weekly', 'monthly'
            start_date: 开始日期，格式'YYYY-MM-DD'
            end_date: 结束日期，格式'YYYY-MM-DD'
            count: 获取条数（当不指定日期时使用）
            
        Returns:
            历史数据DataFrame
        """
        try:
            # 设置默认日期
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=count)).strftime('%Y-%m-%d')
            
            # 转换股票代码
            if stock_code.endswith('.SH'):
                code = stock_code.replace('.SH', '')
                market = 'sh'
            elif stock_code.endswith('.SZ'):
                code = stock_code.replace('.SZ', '')
                market = 'sz'
            else:
                raise ValueError(f"不支持的股票代码格式: {stock_code}")
            
            # 网易财经历史数据API
            period_map = {'daily': 'day', 'weekly': 'week', 'monthly': 'month'}
            period_param = period_map.get(period, 'day')
            
            url = f"http://quotes.money.163.com/service/chddata.html"
            params = {
                'code': f"{market}{code}",
                'start': start_date.replace('-', ''),
                'end': end_date.replace('-', ''),
                'fields': 'TCLOSE;HIGH;LOW;TOPEN;LCLOSE;CHG;PCHG;TURNOVER;VOTURNOVER;VATURNOVER'
            }
            
            response = self.session.get(url, params=params, timeout=15)
            response.encoding = 'gbk'
            
            # 解析CSV数据
            lines = response.text.strip().split('\n')
            if len(lines) < 2:
                return pd.DataFrame()
            
            # 处理表头
            headers = lines[0].split(',')
            data_lines = lines[1:]

            data = []
            for line in data_lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 10:  # 至少需要基本字段
                        data.append(parts)

            if not data:
                return pd.DataFrame()

            df = pd.DataFrame(data)

            # 根据实际列数设置列名
            if len(df.columns) >= 13:
                df.columns = ['date', 'code', 'name', 'close', 'high', 'low', 'open',
                             'pre_close', 'change', 'change_pct', 'turnover', 'volume', 'amount']
            else:
                # 简化列名
                basic_cols = ['date', 'code', 'name', 'close', 'high', 'low', 'open']
                df.columns = basic_cols[:len(df.columns)]
            
            # 转换数据类型
            numeric_cols = ['close', 'high', 'low', 'open']
            if 'pre_close' in df.columns:
                numeric_cols.extend(['pre_close', 'change', 'change_pct'])
            if 'volume' in df.columns:
                numeric_cols.extend(['volume'])
            if 'turnover' in df.columns:
                numeric_cols.extend(['turnover'])
            if 'amount' in df.columns:
                numeric_cols.extend(['amount'])

            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            print(f"获取 {stock_code} 历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_market_overview(self) -> Dict:
        """
        获取市场概览
        
        Returns:
            市场指数数据
        """
        try:
            # 获取主要指数
            indices = ['sh000001', 'sz399001', 'sz399006']  # 上证指数、深证成指、创业板指
            url = f"http://hq.sinajs.cn/list={','.join(indices)}"
            
            response = self.session.get(url, timeout=10)
            response.encoding = 'gbk'
            
            result = {}
            lines = response.text.strip().split('\n')
            index_names = ['上证指数', '深证成指', '创业板指']
            
            for i, line in enumerate(lines):
                if i >= len(index_names):
                    break
                    
                if 'var hq_str_' in line:
                    data_str = line.split('"')[1]
                    if data_str:
                        parts = data_str.split(',')
                        if len(parts) >= 6:
                            result[index_names[i]] = {
                                'current': float(parts[1]) if parts[1] else 0,
                                'change': float(parts[2]) if parts[2] else 0,
                                'change_pct': float(parts[3]) if parts[3] else 0,
                                'volume': int(parts[4]) if parts[4] else 0,
                                'turnover': float(parts[5]) if parts[5] else 0
                            }
            
            return result
            
        except Exception as e:
            print(f"获取市场概览失败: {e}")
            return {}


def demo():
    """使用示例"""
    print("=" * 50)
    print("中国A股数据获取客户端示例")
    print("=" * 50)
    
    # 创建客户端
    client = AStockDataClient()
    
    # 1. 获取股票列表
    print("\n=== 获取股票列表 ===")
    stock_list = client.get_stock_list()
    print(f"获取到 {len(stock_list)} 只股票")
    print("前5只股票:")
    print(stock_list.head())
    
    # 2. 获取实时行情
    print("\n=== 获取实时行情 ===")
    test_stocks = ['000001.SZ', '600000.SH', '600519.SH']
    realtime_data = client.get_realtime_quote(test_stocks)
    
    for stock_code, data in realtime_data.items():
        print(f"\n{stock_code} ({data.get('name', 'N/A')}):")
        print(f"  当前价: {data.get('current', 'N/A')}")
        print(f"  涨跌额: {data.get('change', 'N/A')}")
        print(f"  涨跌幅: {data.get('change_pct', 'N/A'):.2f}%")
        print(f"  成交量: {data.get('volume', 'N/A')}")
        print(f"  更新时间: {data.get('time', 'N/A')}")
    
    # 3. 获取历史数据
    print("\n=== 获取历史数据 ===")
    historical_data = client.get_historical_data('000001.SZ', count=10)
    if not historical_data.empty:
        print("平安银行最近10天数据:")
        print(historical_data[['date', 'open', 'high', 'low', 'close', 'volume']].tail())
    else:
        print("未获取到历史数据")
    
    # 4. 获取市场概览
    print("\n=== 市场概览 ===")
    market_data = client.get_market_overview()
    for index_name, data in market_data.items():
        print(f"{index_name}: {data.get('current', 'N/A')} "
              f"({data.get('change', 'N/A'):+.2f}, {data.get('change_pct', 'N/A'):+.2f}%)")
    
    print("\n" + "=" * 50)
    print("示例运行完成")


if __name__ == "__main__":
    demo()
