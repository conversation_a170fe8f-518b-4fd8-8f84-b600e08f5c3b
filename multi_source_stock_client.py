# -*- coding: utf-8 -*-
"""
多数据源A股客户端
支持多个数据源，自动切换最可靠的API
"""

import requests
import json
import time
from datetime import datetime
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MultiSourceStockClient:
    """多数据源股票数据客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 数据源配置
        self.data_sources = {
            'tencent': {
                'name': '腾讯财经',
                'enabled': True,
                'priority': 3,
                'base_url': 'https://qt.gtimg.cn/q=',
                'timeout': 5
            },
            'sina': {
                'name': '新浪财经',
                'enabled': True,
                'priority': 2,
                'base_url': 'https://hq.sinajs.cn/list=',
                'timeout': 5
            },
            'eastmoney': {
                'name': '东方财富',
                'enabled': True,
                'priority': 1,
                'base_url': 'https://push2.eastmoney.com/api/qt/stock/get',
                'timeout': 5
            },
            'thinktrader': {
                'name': '迅投行情',
                'enabled': True,  # 启用普通行情站点
                'priority': 0,    # 最高优先级
                'servers': [
                    ('shmd1.thinktrader.net', 55300),  # 迅投浦东电信1
                    ('shmd2.thinktrader.net', 55300),  # 迅投浦东电信2
                    ('szmd1.thinktrader.net', 55300),  # 迅投东莞电信1
                    ('szmd2.thinktrader.net', 55300),  # 迅投东莞电信2
                    ('*************', 55300),          # 备用IP1
                    ('*************', 55300),          # 备用IP2
                    ('**************', 55300),         # 备用IP3
                    ('**************', 55300),         # 备用IP4
                ],
                'timeout': 10
            }
        }
    
    def _convert_stock_code(self, code: str, source: str) -> str:
        """转换股票代码格式"""
        if source == 'tencent':
            # 腾讯格式: sz000001, sh600000
            if code.endswith('.SZ'):
                return 'sz' + code[:6]
            elif code.endswith('.SH'):
                return 'sh' + code[:6]
        elif source == 'sina':
            # 新浪格式: sz000001, sh600000
            if code.endswith('.SZ'):
                return 'sz' + code[:6]
            elif code.endswith('.SH'):
                return 'sh' + code[:6]
        elif source == 'eastmoney':
            # 东方财富格式: 0.000001, 1.600000
            if code.endswith('.SZ'):
                return f"0.{code[:6]}"
            elif code.endswith('.SH'):
                return f"1.{code[:6]}"
        
        return code
    
    def _get_tencent_data(self, stock_codes: List[str]) -> Dict:
        """获取腾讯财经数据"""
        try:
            converted_codes = [self._convert_stock_code(code, 'tencent') for code in stock_codes]
            url = self.data_sources['tencent']['base_url'] + ','.join(converted_codes)
            
            response = self.session.get(url, timeout=self.data_sources['tencent']['timeout'])
            response.raise_for_status()
            
            data = {}
            lines = response.text.strip().split('\n')
            
            for i, line in enumerate(lines):
                if '~' in line:
                    parts = line.split('~')
                    if len(parts) >= 47:
                        original_code = stock_codes[i]
                        data[original_code] = {
                            'name': parts[1],
                            'current': float(parts[3]) if parts[3] else 0.0,
                            'pre_close': float(parts[4]) if parts[4] else 0.0,
                            'open': float(parts[5]) if parts[5] else 0.0,
                            'high': float(parts[33]) if parts[33] else 0.0,
                            'low': float(parts[34]) if parts[34] else 0.0,
                            'volume': int(parts[36]) if parts[36] else 0,
                            'turnover': float(parts[37]) if parts[37] else 0.0,
                            'change': float(parts[31]) if parts[31] else 0.0,
                            'change_pct': float(parts[32]) if parts[32] else 0.0,
                            'time': parts[30] if len(parts) > 30 else '',
                            'source': '腾讯财经'
                        }
            
            logger.info(f"腾讯财经API获取成功，股票数量: {len(data)}")
            return data
            
        except Exception as e:
            logger.error(f"腾讯财经API失败: {str(e)}")
            return {}
    
    def _get_sina_data(self, stock_codes: List[str]) -> Dict:
        """获取新浪财经数据"""
        try:
            converted_codes = [self._convert_stock_code(code, 'sina') for code in stock_codes]
            url = self.data_sources['sina']['base_url'] + ','.join(converted_codes)
            
            response = self.session.get(url, timeout=self.data_sources['sina']['timeout'])
            response.raise_for_status()
            
            data = {}
            lines = response.text.strip().split('\n')
            
            for i, line in enumerate(lines):
                if 'str_' in line and '=' in line:
                    content = line.split('="')[1].rstrip('";')
                    parts = content.split(',')
                    
                    if len(parts) >= 32:
                        original_code = stock_codes[i]
                        current = float(parts[3]) if parts[3] else 0.0
                        pre_close = float(parts[2]) if parts[2] else 0.0
                        
                        data[original_code] = {
                            'name': parts[0],
                            'current': current,
                            'pre_close': pre_close,
                            'open': float(parts[1]) if parts[1] else 0.0,
                            'high': float(parts[4]) if parts[4] else 0.0,
                            'low': float(parts[5]) if parts[5] else 0.0,
                            'volume': int(parts[8]) if parts[8] else 0,
                            'turnover': float(parts[9]) if parts[9] else 0.0,
                            'change': current - pre_close,
                            'change_pct': ((current - pre_close) / pre_close * 100) if pre_close > 0 else 0.0,
                            'time': f"{parts[30]} {parts[31]}",
                            'source': '新浪财经'
                        }
            
            logger.info(f"新浪财经API获取成功，股票数量: {len(data)}")
            return data
            
        except Exception as e:
            logger.error(f"新浪财经API失败: {str(e)}")
            return {}
    
    def _get_eastmoney_data(self, stock_codes: List[str]) -> Dict:
        """获取东方财富数据"""
        try:
            data = {}
            
            for code in stock_codes:
                converted_code = self._convert_stock_code(code, 'eastmoney')
                url = self.data_sources['eastmoney']['base_url']
                
                params = {
                    'secid': converted_code,
                    'fields': 'f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f57,f58,f169,f170,f46,f44,f51,f52,f47,f48'
                }
                
                response = self.session.get(url, params=params, timeout=self.data_sources['eastmoney']['timeout'])
                response.raise_for_status()
                
                result = response.json()
                if result.get('rc') == 0 and result.get('data'):
                    stock_data = result['data']
                    
                    data[code] = {
                        'name': stock_data.get('f58', ''),
                        'current': float(stock_data.get('f43', 0)) / 100,
                        'pre_close': float(stock_data.get('f60', 0)) / 100,
                        'open': float(stock_data.get('f46', 0)) / 100,
                        'high': float(stock_data.get('f44', 0)) / 100,
                        'low': float(stock_data.get('f45', 0)) / 100,
                        'volume': int(stock_data.get('f47', 0)),
                        'turnover': float(stock_data.get('f48', 0)),
                        'change': float(stock_data.get('f169', 0)) / 100,
                        'change_pct': float(stock_data.get('f170', 0)) / 100,
                        'time': datetime.now().strftime('%H:%M:%S'),
                        'source': '东方财富'
                    }
            
            logger.info(f"东方财富API获取成功，股票数量: {len(data)}")
            return data
            
        except Exception as e:
            logger.error(f"东方财富API失败: {str(e)}")
            return {}
    
    def get_realtime_quote(self, stock_codes: List[str]) -> Dict:
        """
        获取实时行情 - 多数据源自动切换
        
        Args:
            stock_codes: 股票代码列表，如['000001.SZ', '600000.SH']
            
        Returns:
            Dict: 股票行情数据
        """
        logger.info(f"开始获取实时行情，股票代码: {stock_codes}")
        
        # 按优先级排序数据源
        sorted_sources = sorted(
            [(name, config) for name, config in self.data_sources.items() if config['enabled']],
            key=lambda x: x[1]['priority']
        )
        
        for source_name, source_config in sorted_sources:
            try:
                logger.info(f"尝试使用数据源: {source_config['name']}")
                
                if source_name == 'tencent':
                    data = self._get_tencent_data(stock_codes)
                elif source_name == 'sina':
                    data = self._get_sina_data(stock_codes)
                elif source_name == 'eastmoney':
                    data = self._get_eastmoney_data(stock_codes)
                else:
                    continue
                
                if data:
                    logger.info(f"✅ 使用 {source_config['name']} 获取数据成功")
                    return data
                    
            except Exception as e:
                logger.warning(f"❌ {source_config['name']} 数据源失败: {str(e)}")
                continue
        
        logger.error("❌ 所有数据源都失败，返回空数据")
        return {}
    
    def get_market_indices(self) -> Dict:
        """获取市场指数"""
        indices_codes = ['sh000001', 'sz399001', 'sz399006']  # 上证指数、深证成指、创业板指
        
        try:
            url = self.data_sources['tencent']['base_url'] + ','.join(indices_codes)
            response = self.session.get(url, timeout=5)
            response.raise_for_status()
            
            data = {}
            lines = response.text.strip().split('\n')
            names = ['上证指数', '深证成指', '创业板指']
            
            for i, line in enumerate(lines):
                if '~' in line and i < len(names):
                    parts = line.split('~')
                    if len(parts) >= 47:
                        current = float(parts[3]) if parts[3] else 0.0
                        pre_close = float(parts[4]) if parts[4] else 0.0
                        change = current - pre_close
                        change_pct = (change / pre_close * 100) if pre_close > 0 else 0.0
                        
                        data[names[i]] = {
                            'current': current,
                            'change': change,
                            'change_pct': change_pct,
                            'volume': int(parts[36]) if parts[36] else 0,
                        }
            
            return data
            
        except Exception as e:
            logger.error(f"获取市场指数失败: {str(e)}")
            return {}


def main():
    """演示多数据源客户端"""
    print("=" * 50)
    print("多数据源A股数据客户端演示")
    print("=" * 50)
    
    client = MultiSourceStockClient()
    
    # 测试股票代码
    test_stocks = ['000001.SZ', '600000.SH', '600519.SH']
    
    print("\n=== 实时行情 (多数据源) ===\n")
    
    quotes = client.get_realtime_quote(test_stocks)
    
    for code, data in quotes.items():
        print(f"{code} - {data['name']}")
        print(f"当前价: {data['current']:.2f}")
        print(f"涨跌: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
        print(f"成交量: {data['volume']:,}")
        print(f"数据源: {data['source']}")
        print("-" * 30)
    
    print("\n=== 市场指数 ===")
    indices = client.get_market_indices()
    for name, data in indices.items():
        print(f"{name}: {data['current']:.2f} ({data['change']:+.2f}, {data['change_pct']:+.2f}%)")
    
    print("\n" + "=" * 50)
    print("演示完成")


if __name__ == "__main__":
    main()
