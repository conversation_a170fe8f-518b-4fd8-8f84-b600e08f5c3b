import { useState, useEffect } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { Calendar, TrendingUp, BarChart3 } from 'lucide-react'
import stockService from '../services/stockService'

const StockChart = ({ selectedStock }) => {
  const [historicalData, setHistoricalData] = useState([])
  const [loading, setLoading] = useState(false)
  const [chartType, setChartType] = useState('line') // 'line' or 'bar'
  const [timeRange, setTimeRange] = useState(30) // 天数

  // 获取历史数据
  const fetchHistoricalData = async (stockCode) => {
    if (!stockCode) return
    
    setLoading(true)
    try {
      const data = await stockService.getHistoricalData(stockCode, timeRange)
      setHistoricalData(data)
    } catch (error) {
      console.error('获取历史数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (selectedStock?.code) {
      fetchHistoricalData(selectedStock.code)
    }
  }, [selectedStock, timeRange])

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">开盘:</span>
              <span className="font-medium">¥{data.open?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">最高:</span>
              <span className="font-medium text-red-600">¥{data.high?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">最低:</span>
              <span className="font-medium text-green-600">¥{data.low?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">收盘:</span>
              <span className="font-medium">¥{data.close?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">成交量:</span>
              <span className="font-medium">{(data.volume / 10000).toFixed(1)}万</span>
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  if (!selectedStock) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center text-gray-500">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">选择股票查看图表</p>
          <p className="text-sm mt-2">点击股票列表中的眼睛图标</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* 图表头部 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {selectedStock.name}
            </h3>
            <p className="text-sm text-gray-500">{selectedStock.code}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              ¥{selectedStock.current?.toFixed(2)}
            </div>
            <div className={`text-sm font-medium ${
              selectedStock.change > 0 ? 'text-stock-green' : 
              selectedStock.change < 0 ? 'text-stock-red' : 'text-gray-500'
            }`}>
              {selectedStock.change > 0 ? '+' : ''}{selectedStock.change?.toFixed(2)} 
              ({selectedStock.change_pct > 0 ? '+' : ''}{selectedStock.change_pct?.toFixed(2)}%)
            </div>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(Number(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={7}>7天</option>
              <option value={30}>30天</option>
              <option value={90}>90天</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setChartType('line')}
              className={`px-3 py-1 text-xs rounded ${
                chartType === 'line' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              线图
            </button>
            <button
              onClick={() => setChartType('bar')}
              className={`px-3 py-1 text-xs rounded ${
                chartType === 'bar' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              柱图
            </button>
          </div>
        </div>
      </div>

      {/* 图表内容 */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">加载图表数据...</span>
          </div>
        ) : historicalData.length > 0 ? (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'line' ? (
                <LineChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    domain={['dataMin - 1', 'dataMax + 1']}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="close" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, stroke: '#3b82f6', strokeWidth: 2 }}
                  />
                </LineChart>
              ) : (
                <BarChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="volume" fill="#8884d8" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>暂无图表数据</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default StockChart
