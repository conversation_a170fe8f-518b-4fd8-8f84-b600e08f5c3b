# -*- coding: utf-8 -*-
"""
A股实时监控工具
无需安装额外客户端，基于免费API
"""

import time
import os
from datetime import datetime
from a_stock_data_client import AStockDataClient


class StockMonitor:
    """股票实时监控"""
    
    def __init__(self, stock_codes: list):
        self.client = AStockDataClient()
        self.stock_codes = stock_codes
        self.running = False
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def format_change(self, change, change_pct):
        """格式化涨跌显示"""
        if change > 0:
            return f"+{change:.2f} (+{change_pct:.2f}%)"
        elif change < 0:
            return f"{change:.2f} ({change_pct:.2f}%)"
        else:
            return f"{change:.2f} ({change_pct:.2f}%)"
    
    def display_data(self, data):
        """显示数据"""
        self.clear_screen()
        
        print("=" * 80)
        print(f"A股实时行情监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        print(f"{'股票代码':<12} {'股票名称':<10} {'当前价':<8} {'涨跌':<15} {'成交量':<12} {'更新时间':<12}")
        print("-" * 80)
        
        for stock_code in self.stock_codes:
            if stock_code in data:
                info = data[stock_code]
                change_str = self.format_change(info.get('change', 0), info.get('change_pct', 0))
                
                print(f"{stock_code:<12} {info.get('name', 'N/A'):<10} "
                      f"{info.get('current', 'N/A'):<8} {change_str:<15} "
                      f"{info.get('volume', 'N/A'):<12} {info.get('time', 'N/A'):<12}")
            else:
                print(f"{stock_code:<12} {'N/A':<10} {'N/A':<8} {'N/A':<15} {'N/A':<12} {'N/A':<12}")
        
        print("-" * 80)
        print("按 Ctrl+C 停止监控")
    
    def start_monitor(self, interval=5):
        """
        开始监控
        
        Args:
            interval: 刷新间隔（秒）
        """
        self.running = True
        print(f"开始监控股票: {', '.join(self.stock_codes)}")
        print(f"刷新间隔: {interval}秒")
        
        try:
            while self.running:
                # 获取实时数据
                data = self.client.get_realtime_quote(self.stock_codes)
                
                # 显示数据
                self.display_data(data)
                
                # 等待
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n监控出错: {e}")
            self.running = False
    
    def stop_monitor(self):
        """停止监控"""
        self.running = False


def get_popular_stocks():
    """获取热门股票列表"""
    return [
        '000001.SZ',  # 平安银行
        '000002.SZ',  # 万科A
        '600000.SH',  # 浦发银行
        '600036.SH',  # 招商银行
        '600519.SH',  # 贵州茅台
        '000858.SZ',  # 五粮液
        '002415.SZ',  # 海康威视
        '300059.SZ',  # 东方财富
        '600276.SH',  # 恒瑞医药
        '002594.SZ',  # 比亚迪
    ]


def interactive_monitor():
    """交互式监控"""
    print("=" * 50)
    print("A股实时行情监控工具")
    print("=" * 50)
    
    while True:
        print("\n请选择监控模式:")
        print("1. 监控热门股票")
        print("2. 自定义股票列表")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            stocks = get_popular_stocks()
            print(f"\n将监控以下热门股票: {', '.join(stocks)}")
            
        elif choice == '2':
            print("\n请输入股票代码，用逗号分隔 (格式: 000001.SZ,600000.SH):")
            stock_input = input().strip()
            if stock_input:
                stocks = [code.strip() for code in stock_input.split(',')]
                print(f"\n将监控: {', '.join(stocks)}")
            else:
                print("未输入股票代码")
                continue
                
        elif choice == '3':
            print("再见!")
            break
            
        else:
            print("无效选择，请重新输入")
            continue
        
        # 设置刷新间隔
        try:
            interval = int(input("\n请输入刷新间隔(秒，默认5): ") or "5")
        except ValueError:
            interval = 5
        
        # 开始监控
        monitor = StockMonitor(stocks)
        monitor.start_monitor(interval)


def quick_quote(stock_codes):
    """快速查询行情"""
    client = AStockDataClient()
    data = client.get_realtime_quote(stock_codes)
    
    print("=" * 60)
    print("实时行情查询")
    print("=" * 60)
    
    for stock_code in stock_codes:
        if stock_code in data:
            info = data[stock_code]
            change = info.get('change', 0)
            change_pct = info.get('change_pct', 0)
            
            print(f"\n{stock_code} - {info.get('name', 'N/A')}")
            print(f"当前价: {info.get('current', 'N/A')}")
            print(f"涨跌: {change:+.2f} ({change_pct:+.2f}%)")
            print(f"今日: 开盘 {info.get('open', 'N/A')} | 最高 {info.get('high', 'N/A')} | 最低 {info.get('low', 'N/A')}")
            print(f"成交量: {info.get('volume', 'N/A')} | 成交额: {info.get('turnover', 'N/A'):.0f}")
            print(f"更新时间: {info.get('time', 'N/A')}")
        else:
            print(f"\n{stock_code} - 未获取到数据")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式：python stock_monitor.py 000001.SZ 600000.SH
        stock_codes = sys.argv[1:]
        print(f"查询股票: {', '.join(stock_codes)}")
        quick_quote(stock_codes)
    else:
        # 交互模式
        interactive_monitor()
