# -*- coding: utf-8 -*-
"""
全市场A股实时行情数据获取模块
支持获取全部A股（约5000只）的实时行情数据
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime
import time
import logging
from typing import Dict, List, Optional, Tuple
import json
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MarketDataFetcher:
    """全市场A股实时行情数据获取器"""
    
    def __init__(self, max_workers: int = 10):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        })
        self.max_workers = max_workers
        self.stock_list_cache = None
        self.last_update_time = None
        self._lock = threading.Lock()
        
        # 数据源配置
        self.data_sources = {
            'sina_list': {
                'name': '新浪股票列表',
                'url': 'http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData',
                'params': {
                    'page': 1,
                    'num': 5000,
                    'sort': 'symbol',
                    'asc': 1,
                    'node': 'hs_a'  # 沪深A股
                }
            },
            'eastmoney_list': {
                'name': '东方财富股票列表',
                'url': 'http://80.push2.eastmoney.com/api/qt/clist/get',
                'markets': {
                    'sh_main': 'm:1+t:2,m:1+t:23',      # 上海主板+科创板
                    'sz_main': 'm:0+t:6,m:0+t:80',      # 深圳主板+中小板
                    'sz_gem': 'm:0+t:81+s:2048',        # 创业板
                },
                'base_params': {
                    'pn': 1,
                    'pz': 5000,
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
                }
            },
            'eastmoney_quote': {
                'name': '东方财富实时行情',
                'url': 'http://push2.eastmoney.com/api/qt/ulist.np/get',
                'batch_size': 100  # 每批次查询的股票数量
            }
        }
    
    def get_all_stock_list_from_sina(self) -> pd.DataFrame:
        """从新浪获取完整的A股股票列表"""
        logger.info("从新浪获取全部A股股票列表...")

        try:
            response = self.session.get(
                self.data_sources['sina_list']['url'],
                params=self.data_sources['sina_list']['params'],
                timeout=15
            )
            response.raise_for_status()

            # 新浪返回的是JavaScript格式，需要处理
            content = response.text
            if content.startswith('var'):
                # 提取JSON部分
                start = content.find('[')
                end = content.rfind(']') + 1
                if start > 0 and end > start:
                    import json
                    stocks_data = json.loads(content[start:end])
                else:
                    raise ValueError("无法解析新浪数据格式")
            else:
                import json
                stocks_data = json.loads(content)

            # 解析股票数据
            stock_list = []
            for stock in stocks_data:
                try:
                    code = stock.get('symbol', '')
                    if not code:
                        continue

                    # 添加交易所后缀
                    if code.startswith('6'):
                        full_code = f"{code}.SH"
                        market = '上海'
                    elif code.startswith(('0', '2', '3')):
                        full_code = f"{code}.SZ"
                        market = '深圳'
                    else:
                        continue

                    stock_info = {
                        'code': full_code,
                        'name': stock.get('name', ''),
                        'current': float(stock.get('trade', 0) or 0),
                        'change': float(stock.get('pricechange', 0) or 0),
                        'change_pct': float(stock.get('changepercent', 0) or 0),
                        'volume': int(float(stock.get('volume', 0) or 0)),
                        'amount': float(stock.get('amount', 0) or 0),
                        'high': float(stock.get('high', 0) or 0),
                        'low': float(stock.get('low', 0) or 0),
                        'open': float(stock.get('open', 0) or 0),
                        'prev_close': float(stock.get('settlement', 0) or 0),
                        'market_cap': 0,  # 新浪数据中没有
                        'pe_ratio': 0,
                        'pb_ratio': 0,
                        'turnover_rate': float(stock.get('turnoverratio', 0) or 0),
                        'amplitude': 0,
                        'market': market,
                        'update_time': datetime.now()
                    }
                    stock_list.append(stock_info)

                except Exception as e:
                    logger.warning(f"解析新浪股票数据失败: {stock}, 错误: {e}")
                    continue

            df = pd.DataFrame(stock_list)
            logger.info(f"从新浪成功获取 {len(df)} 只A股股票信息")
            return df

        except Exception as e:
            logger.error(f"从新浪获取股票列表失败: {e}")
            return pd.DataFrame()

    def get_all_stock_list(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        获取全部A股股票列表
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            包含股票代码、名称等信息的DataFrame
        """
        # 检查缓存
        if not force_refresh and self.stock_list_cache is not None:
            if self.last_update_time and (datetime.now() - self.last_update_time).seconds < 3600:
                logger.info("使用缓存的股票列表")
                return self.stock_list_cache
        
        logger.info("获取全部A股股票列表...")

        try:
            all_stocks_data = []

            # 分市场获取股票列表
            for market_name, market_code in self.data_sources['eastmoney_list']['markets'].items():
                logger.info(f"获取 {market_name} 股票列表...")

                params = self.data_sources['eastmoney_list']['base_params'].copy()
                params['fs'] = market_code

                response = self.session.get(
                    self.data_sources['eastmoney_list']['url'],
                    params=params,
                    timeout=15
                )
                response.raise_for_status()

                data = response.json()
                if data.get('rc') != 0 or 'data' not in data:
                    logger.warning(f"获取 {market_name} 失败: {data}")
                    continue

                market_stocks = data['data']['diff']
                if market_stocks:
                    all_stocks_data.extend(market_stocks)
                    logger.info(f"{market_name} 获取到 {len(market_stocks)} 只股票")

                time.sleep(0.2)  # 避免请求过快

            stocks_data = all_stocks_data
            logger.info(f"总共获取 {len(stocks_data)} 只股票数据")
            
            # 解析股票数据
            stock_list = []
            for stock in stocks_data:
                try:
                    # 构建股票代码（添加交易所后缀）
                    code = stock.get('f12', '')
                    market = stock.get('f13', 0)
                    
                    if market == 0:  # 深圳
                        full_code = f"{code}.SZ"
                    elif market == 1:  # 上海
                        full_code = f"{code}.SH"
                    else:
                        continue  # 跳过其他市场
                    
                    stock_info = {
                        'code': full_code,
                        'name': stock.get('f14', ''),
                        'current': stock.get('f2', 0),  # 当前价
                        'change': stock.get('f4', 0),   # 涨跌额
                        'change_pct': stock.get('f3', 0),  # 涨跌幅
                        'volume': stock.get('f5', 0),   # 成交量
                        'amount': stock.get('f6', 0),   # 成交额
                        'high': stock.get('f15', 0),    # 最高价
                        'low': stock.get('f16', 0),     # 最低价
                        'open': stock.get('f17', 0),    # 开盘价
                        'prev_close': stock.get('f18', 0),  # 昨收价
                        'market_cap': stock.get('f20', 0),  # 总市值
                        'pe_ratio': stock.get('f9', 0),     # 市盈率
                        'pb_ratio': stock.get('f23', 0),    # 市净率
                        'turnover_rate': stock.get('f8', 0), # 换手率
                        'amplitude': stock.get('f7', 0),     # 振幅
                        'market': '深圳' if market == 0 else '上海',
                        'update_time': datetime.now()
                    }
                    stock_list.append(stock_info)
                    
                except Exception as e:
                    logger.warning(f"解析股票数据失败: {stock}, 错误: {e}")
                    continue
            
            # 转换为DataFrame
            df = pd.DataFrame(stock_list)
            
            # 缓存结果
            with self._lock:
                self.stock_list_cache = df
                self.last_update_time = datetime.now()
            
            logger.info(f"成功获取 {len(df)} 只A股股票信息")
            return df
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            if self.stock_list_cache is not None:
                logger.info("使用缓存的股票列表")
                return self.stock_list_cache
            return pd.DataFrame()
    
    def get_stock_codes_list(self) -> List[str]:
        """获取所有股票代码列表"""
        df = self.get_all_stock_list()
        return df['code'].tolist() if not df.empty else []
    
    def get_realtime_quotes_batch(self, stock_codes: List[str]) -> Dict[str, Dict]:
        """
        批量获取实时行情数据
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            股票行情数据字典
        """
        if not stock_codes:
            return {}
        
        logger.info(f"批量获取 {len(stock_codes)} 只股票的实时行情")
        
        # 分批处理
        batch_size = self.data_sources['eastmoney_quote']['batch_size']
        all_quotes = {}
        
        # 使用线程池并发获取
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]
                future = executor.submit(self._get_batch_quotes, batch_codes)
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    batch_result = future.result(timeout=30)
                    all_quotes.update(batch_result)
                except Exception as e:
                    logger.error(f"批次获取失败: {e}")
        
        logger.info(f"成功获取 {len(all_quotes)} 只股票的行情数据")
        return all_quotes
    
    def _get_batch_quotes(self, stock_codes: List[str]) -> Dict[str, Dict]:
        """获取单批次股票行情"""
        try:
            # 构建查询参数
            secids = []
            for code in stock_codes:
                if code.endswith('.SZ'):
                    secids.append(f"0.{code[:-3]}")
                elif code.endswith('.SH'):
                    secids.append(f"1.{code[:-3]}")
            
            if not secids:
                return {}
            
            params = {
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f62,f128,f136,f115,f152',
                'secids': ','.join(secids)
            }
            
            response = self.session.get(
                self.data_sources['eastmoney_quote']['url'],
                params=params,
                timeout=15
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('rc') != 0 or 'data' not in data:
                logger.warning(f"批次查询返回错误: {data}")
                return {}
            
            # 解析数据
            quotes = {}
            for item in data['data']['diff']:
                try:
                    code = item.get('f12', '')
                    market = item.get('f13', 0)
                    
                    if market == 0:
                        full_code = f"{code}.SZ"
                    elif market == 1:
                        full_code = f"{code}.SH"
                    else:
                        continue
                    
                    quotes[full_code] = {
                        'name': item.get('f14', ''),
                        'current': item.get('f2', 0),
                        'change': item.get('f4', 0),
                        'change_pct': item.get('f3', 0),
                        'volume': item.get('f5', 0),
                        'amount': item.get('f6', 0),
                        'high': item.get('f15', 0),
                        'low': item.get('f16', 0),
                        'open': item.get('f17', 0),
                        'prev_close': item.get('f18', 0),
                        'market_cap': item.get('f20', 0),
                        'pe_ratio': item.get('f9', 0),
                        'pb_ratio': item.get('f23', 0),
                        'turnover_rate': item.get('f8', 0),
                        'amplitude': item.get('f7', 0),
                        'source': '东方财富',
                        'update_time': datetime.now().strftime('%H:%M:%S')
                    }
                    
                except Exception as e:
                    logger.warning(f"解析单只股票数据失败: {item}, 错误: {e}")
                    continue
            
            return quotes
            
        except Exception as e:
            logger.error(f"获取批次行情失败: {e}")
            return {}
    
    def get_all_market_quotes(self) -> Dict[str, Dict]:
        """获取全市场实时行情"""
        logger.info("开始获取全市场A股实时行情...")
        
        # 获取所有股票代码
        stock_codes = self.get_stock_codes_list()
        if not stock_codes:
            logger.error("无法获取股票代码列表")
            return {}
        
        # 批量获取行情
        return self.get_realtime_quotes_batch(stock_codes)
    
    def get_market_summary(self) -> Dict:
        """获取市场概况"""
        try:
            # 获取主要指数
            indices_codes = ['000001.SH', '399001.SZ', '399006.SZ']  # 上证指数、深证成指、创业板指
            indices_data = self.get_realtime_quotes_batch(indices_codes)
            
            # 获取全市场统计
            all_quotes = self.get_all_market_quotes()
            
            if not all_quotes:
                return {}
            
            # 计算市场统计
            prices = [q['current'] for q in all_quotes.values() if q['current'] > 0]
            changes = [q['change_pct'] for q in all_quotes.values()]
            
            up_count = sum(1 for change in changes if change > 0)
            down_count = sum(1 for change in changes if change < 0)
            flat_count = sum(1 for change in changes if change == 0)
            
            summary = {
                'total_stocks': len(all_quotes),
                'up_count': up_count,
                'down_count': down_count,
                'flat_count': flat_count,
                'up_ratio': up_count / len(all_quotes) * 100 if all_quotes else 0,
                'avg_change_pct': np.mean(changes) if changes else 0,
                'indices': indices_data,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取市场概况失败: {e}")
            return {}


def main():
    """测试函数"""
    print("=" * 60)
    print("全市场A股实时行情数据获取测试")
    print("=" * 60)
    
    fetcher = MarketDataFetcher(max_workers=5)
    
    # 测试1: 获取股票列表
    print("\n1. 获取全部A股股票列表...")
    stock_list = fetcher.get_all_stock_list()
    print(f"获取到 {len(stock_list)} 只股票")
    if not stock_list.empty:
        print("\n前10只股票:")
        print(stock_list.head(10)[['code', 'name', 'current', 'change_pct']].to_string())
    
    # 测试2: 获取部分股票实时行情
    print("\n2. 获取部分股票实时行情...")
    test_codes = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH']
    quotes = fetcher.get_realtime_quotes_batch(test_codes)
    
    for code, data in quotes.items():
        print(f"{code} - {data['name']}: ¥{data['current']:.2f} ({data['change_pct']:+.2f}%)")
    
    # 测试3: 获取市场概况
    print("\n3. 获取市场概况...")
    summary = fetcher.get_market_summary()
    if summary:
        print(f"总股票数: {summary['total_stocks']}")
        print(f"上涨: {summary['up_count']} ({summary['up_ratio']:.1f}%)")
        print(f"下跌: {summary['down_count']}")
        print(f"平盘: {summary['flat_count']}")
        print(f"平均涨跌幅: {summary['avg_change_pct']:.2f}%")
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
