# -*- coding: utf-8 -*-
"""
A股回测引擎
基于真实历史数据的量化交易回测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OrderType(Enum):
    """订单类型"""
    BUY = "买入"
    SELL = "卖出"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "待成交"
    FILLED = "已成交"
    CANCELLED = "已取消"


@dataclass
class Order:
    """订单类"""
    symbol: str
    order_type: OrderType
    quantity: int
    price: float
    timestamp: datetime
    status: OrderStatus = OrderStatus.PENDING
    order_id: str = ""
    
    def __post_init__(self):
        if not self.order_id:
            self.order_id = f"{self.symbol}_{self.timestamp.strftime('%Y%m%d_%H%M%S')}"


@dataclass
class Position:
    """持仓类"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float = 0.0
    
    @property
    def market_value(self) -> float:
        """市值"""
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        """浮动盈亏"""
        return (self.current_price - self.avg_price) * self.quantity
    
    @property
    def unrealized_pnl_pct(self) -> float:
        """浮动盈亏百分比"""
        if self.avg_price == 0:
            return 0.0
        return (self.current_price - self.avg_price) / self.avg_price * 100


class Portfolio:
    """投资组合类"""
    
    def __init__(self, initial_cash: float = 1000000.0):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Dict] = []
        self.daily_returns: List[float] = []
        self.daily_values: List[float] = []
        self.daily_dates: List[datetime] = []
    
    def add_position(self, symbol: str, quantity: int, price: float):
        """添加持仓"""
        if symbol in self.positions:
            # 更新现有持仓
            pos = self.positions[symbol]
            total_cost = pos.quantity * pos.avg_price + quantity * price
            total_quantity = pos.quantity + quantity
            pos.avg_price = total_cost / total_quantity if total_quantity > 0 else 0
            pos.quantity = total_quantity
        else:
            # 新建持仓
            self.positions[symbol] = Position(symbol, quantity, price)
    
    def remove_position(self, symbol: str, quantity: int, price: float) -> float:
        """减少持仓，返回实现盈亏"""
        if symbol not in self.positions:
            return 0.0
        
        pos = self.positions[symbol]
        if quantity >= pos.quantity:
            # 全部卖出
            realized_pnl = (price - pos.avg_price) * pos.quantity
            del self.positions[symbol]
        else:
            # 部分卖出
            realized_pnl = (price - pos.avg_price) * quantity
            pos.quantity -= quantity
        
        return realized_pnl
    
    def update_prices(self, prices: Dict[str, float]):
        """更新持仓价格"""
        for symbol, price in prices.items():
            if symbol in self.positions:
                self.positions[symbol].current_price = price
    
    @property
    def total_value(self) -> float:
        """总资产"""
        positions_value = sum(pos.market_value for pos in self.positions.values())
        return self.cash + positions_value
    
    @property
    def total_return(self) -> float:
        """总收益率"""
        return (self.total_value - self.initial_cash) / self.initial_cash * 100


class Strategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.data: Optional[pd.DataFrame] = None
        self.current_date: Optional[datetime] = None
        self.portfolio: Optional[Portfolio] = None
    
    @abstractmethod
    def initialize(self, data: pd.DataFrame, portfolio: Portfolio):
        """初始化策略"""
        pass
    
    @abstractmethod
    def on_data(self, current_data: pd.Series) -> List[Order]:
        """处理数据，返回订单列表"""
        pass
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        return data


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_cash: float = 1000000.0):
        self.portfolio = Portfolio(initial_cash)
        self.strategy: Optional[Strategy] = None
        self.data: Optional[pd.DataFrame] = None
        self.commission_rate = 0.0003  # 手续费率
        self.slippage = 0.001  # 滑点
        
    def set_strategy(self, strategy: Strategy):
        """设置策略"""
        self.strategy = strategy
    
    def set_data(self, data: pd.DataFrame):
        """设置数据"""
        self.data = data.copy()
        # 确保数据按日期排序
        if 'date' in data.columns:
            self.data = self.data.sort_values('date')
        elif data.index.name == 'date' or isinstance(data.index, pd.DatetimeIndex):
            self.data = self.data.sort_index()
    
    def execute_order(self, order: Order, current_prices: Dict[str, float]) -> bool:
        """执行订单"""
        if order.symbol not in current_prices:
            logger.warning(f"无法获取 {order.symbol} 的价格数据")
            return False
        
        # 计算实际成交价格（考虑滑点）
        market_price = current_prices[order.symbol]
        if order.order_type == OrderType.BUY:
            execution_price = market_price * (1 + self.slippage)
        else:
            execution_price = market_price * (1 - self.slippage)
        
        # 计算手续费
        trade_value = order.quantity * execution_price
        commission = trade_value * self.commission_rate
        
        if order.order_type == OrderType.BUY:
            # 买入
            total_cost = trade_value + commission
            if self.portfolio.cash >= total_cost:
                self.portfolio.cash -= total_cost
                self.portfolio.add_position(order.symbol, order.quantity, execution_price)
                order.status = OrderStatus.FILLED
                
                # 记录交易
                self.portfolio.trades.append({
                    'date': order.timestamp,
                    'symbol': order.symbol,
                    'type': '买入',
                    'quantity': order.quantity,
                    'price': execution_price,
                    'value': trade_value,
                    'commission': commission,
                    'cash_after': self.portfolio.cash
                })
                return True
            else:
                logger.warning(f"资金不足，无法买入 {order.symbol}")
                return False
        
        else:
            # 卖出
            if order.symbol in self.portfolio.positions:
                pos = self.portfolio.positions[order.symbol]
                if pos.quantity >= order.quantity:
                    realized_pnl = self.portfolio.remove_position(
                        order.symbol, order.quantity, execution_price
                    )
                    self.portfolio.cash += trade_value - commission
                    order.status = OrderStatus.FILLED
                    
                    # 记录交易
                    self.portfolio.trades.append({
                        'date': order.timestamp,
                        'symbol': order.symbol,
                        'type': '卖出',
                        'quantity': order.quantity,
                        'price': execution_price,
                        'value': trade_value,
                        'commission': commission,
                        'realized_pnl': realized_pnl,
                        'cash_after': self.portfolio.cash
                    })
                    return True
                else:
                    logger.warning(f"持仓不足，无法卖出 {order.symbol}")
                    return False
            else:
                logger.warning(f"没有 {order.symbol} 的持仓")
                return False
    
    def run_backtest(self, start_date: str = None, end_date: str = None) -> Dict:
        """运行回测"""
        if not self.strategy or self.data is None:
            raise ValueError("请先设置策略和数据")
        
        logger.info(f"开始回测策略: {self.strategy.name}")
        logger.info(f"初始资金: ¥{self.portfolio.initial_cash:,.2f}")
        
        # 初始化策略
        self.strategy.initialize(self.data, self.portfolio)
        
        # 过滤日期范围
        data = self.data.copy()
        if start_date:
            data = data[data.index >= start_date] if isinstance(data.index, pd.DatetimeIndex) else data[data['date'] >= start_date]
        if end_date:
            data = data[data.index <= end_date] if isinstance(data.index, pd.DatetimeIndex) else data[data['date'] <= end_date]
        
        # 逐日回测
        for i, (date, row) in enumerate(data.iterrows()):
            if isinstance(date, str):
                date = pd.to_datetime(date)
            
            self.strategy.current_date = date
            
            # 更新持仓价格
            current_prices = {}
            if 'close' in row:
                # 单股票数据
                symbol = row.get('symbol', '000001.SZ')
                current_prices[symbol] = row['close']
            else:
                # 多股票数据（需要根据实际数据结构调整）
                for col in row.index:
                    if col.endswith('_close'):
                        symbol = col.replace('_close', '')
                        current_prices[symbol] = row[col]
            
            self.portfolio.update_prices(current_prices)
            
            # 策略生成订单
            orders = self.strategy.on_data(row)
            
            # 执行订单
            for order in orders:
                order.timestamp = date
                self.execute_order(order, current_prices)
                self.portfolio.orders.append(order)
            
            # 记录每日净值
            self.portfolio.daily_dates.append(date)
            self.portfolio.daily_values.append(self.portfolio.total_value)
            
            # 计算日收益率
            if len(self.portfolio.daily_values) > 1:
                daily_return = (self.portfolio.daily_values[-1] / self.portfolio.daily_values[-2] - 1) * 100
                self.portfolio.daily_returns.append(daily_return)
            else:
                self.portfolio.daily_returns.append(0.0)
        
        logger.info("回测完成")
        return self.get_backtest_results()
    
    def get_backtest_results(self) -> Dict:
        """获取回测结果"""
        if not self.portfolio.daily_values:
            return {}
        
        # 基本统计
        total_return = self.portfolio.total_return
        total_trades = len(self.portfolio.trades)
        
        # 计算更多指标
        returns = np.array(self.portfolio.daily_returns)
        
        results = {
            'strategy_name': self.strategy.name if self.strategy else 'Unknown',
            'initial_cash': self.portfolio.initial_cash,
            'final_value': self.portfolio.total_value,
            'total_return': total_return,
            'total_trades': total_trades,
            'daily_returns': self.portfolio.daily_returns,
            'daily_values': self.portfolio.daily_values,
            'daily_dates': self.portfolio.daily_dates,
            'trades': self.portfolio.trades,
            'positions': {symbol: {
                'quantity': pos.quantity,
                'avg_price': pos.avg_price,
                'current_price': pos.current_price,
                'market_value': pos.market_value,
                'unrealized_pnl': pos.unrealized_pnl,
                'unrealized_pnl_pct': pos.unrealized_pnl_pct
            } for symbol, pos in self.portfolio.positions.items()},
            'cash': self.portfolio.cash
        }
        
        # 计算风险指标
        if len(returns) > 0:
            results.update({
                'volatility': np.std(returns) * np.sqrt(252),  # 年化波动率
                'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0,
                'max_drawdown': self._calculate_max_drawdown(),
                'win_rate': self._calculate_win_rate()
            })
        
        return results
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        values = np.array(self.portfolio.daily_values)
        if len(values) == 0:
            return 0.0
        
        peak = np.maximum.accumulate(values)
        drawdown = (values - peak) / peak * 100
        return np.min(drawdown)
    
    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if not self.portfolio.trades:
            return 0.0
        
        profitable_trades = sum(1 for trade in self.portfolio.trades 
                              if trade.get('realized_pnl', 0) > 0)
        return profitable_trades / len(self.portfolio.trades) * 100
