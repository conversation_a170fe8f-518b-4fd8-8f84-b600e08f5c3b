@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f8fafc;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 股票价格动画 */
.price-up {
  animation: flash-green 0.5s ease-in-out;
}

.price-down {
  animation: flash-red 0.5s ease-in-out;
}

@keyframes flash-green {
  0% { background-color: transparent; }
  50% { background-color: rgba(46, 213, 115, 0.2); }
  100% { background-color: transparent; }
}

@keyframes flash-red {
  0% { background-color: transparent; }
  50% { background-color: rgba(255, 71, 87, 0.2); }
  100% { background-color: transparent; }
}
