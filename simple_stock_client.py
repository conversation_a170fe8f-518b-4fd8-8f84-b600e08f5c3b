# -*- coding: utf-8 -*-
"""
简化版A股数据客户端
使用腾讯财经API，更稳定可靠
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional


class SimpleStockClient:
    """简化版股票数据客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://gu.qq.com/'
        })
    
    def get_realtime_quote(self, stock_codes: List[str]) -> Dict:
        """
        获取实时行情 - 使用腾讯财经API
        
        Args:
            stock_codes: 股票代码列表，如['000001.SZ', '600000.SH']
            
        Returns:
            实时行情数据字典
        """
        try:
            # 转换为腾讯格式
            tencent_codes = []
            for code in stock_codes:
                if code.endswith('.SH'):
                    tencent_codes.append('sh' + code.replace('.SH', ''))
                elif code.endswith('.SZ'):
                    tencent_codes.append('sz' + code.replace('.SZ', ''))
            
            if not tencent_codes:
                return {}
            
            # 腾讯财经API
            url = f"https://qt.gtimg.cn/q={','.join(tencent_codes)}"
            response = self.session.get(url, timeout=10)
            response.encoding = 'gbk'
            
            result = {}
            lines = response.text.strip().split('\n')
            
            for i, line in enumerate(lines):
                if i >= len(stock_codes):
                    break
                
                if '="' in line and '";' in line:
                    # 提取数据部分
                    data_part = line.split('="')[1].split('";')[0]
                    if data_part and data_part != 'null':
                        parts = data_part.split('~')
                        if len(parts) >= 50:
                            stock_code = stock_codes[i]
                            current_price = float(parts[3]) if parts[3] else 0
                            pre_close = float(parts[4]) if parts[4] else 0
                            
                            result[stock_code] = {
                                'name': parts[1],
                                'current': current_price,
                                'pre_close': pre_close,
                                'open': float(parts[5]) if parts[5] else 0,
                                'high': float(parts[33]) if parts[33] else 0,
                                'low': float(parts[34]) if parts[34] else 0,
                                'volume': int(parts[6]) if parts[6] else 0,
                                'turnover': float(parts[37]) if parts[37] else 0,
                                'change': current_price - pre_close,
                                'change_pct': ((current_price - pre_close) / pre_close * 100) if pre_close > 0 else 0,
                                'time': parts[30] if len(parts) > 30 else ''
                            }
            
            return result
            
        except Exception as e:
            print(f"获取实时行情失败: {e}")
            return {}
    
    def get_historical_data_simple(self, stock_code: str, days: int = 30) -> pd.DataFrame:
        """
        获取简单历史数据
        
        Args:
            stock_code: 股票代码
            days: 天数
            
        Returns:
            历史数据DataFrame
        """
        try:
            # 模拟历史数据（实际应用中可以接入其他数据源）
            end_date = datetime.now()
            dates = []
            prices = []
            
            # 获取当前价格作为基准
            current_data = self.get_realtime_quote([stock_code])
            if stock_code in current_data:
                base_price = current_data[stock_code]['current']
            else:
                base_price = 10.0  # 默认价格
            
            # 生成模拟历史数据
            import random
            random.seed(hash(stock_code) % 1000)  # 使用股票代码作为种子，保证数据一致性
            
            for i in range(days):
                date = end_date - timedelta(days=days-i-1)
                # 模拟价格波动
                change_pct = random.uniform(-0.05, 0.05)  # ±5%波动
                price = base_price * (1 + change_pct * (i / days))
                
                dates.append(date.strftime('%Y-%m-%d'))
                prices.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'open': price * random.uniform(0.98, 1.02),
                    'high': price * random.uniform(1.00, 1.05),
                    'low': price * random.uniform(0.95, 1.00),
                    'close': price,
                    'volume': random.randint(1000000, 10000000)
                })
            
            df = pd.DataFrame(prices)
            df['date'] = pd.to_datetime(df['date'])
            return df.sort_values('date').reset_index(drop=True)
            
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_market_indices(self) -> Dict:
        """获取主要市场指数"""
        try:
            indices = ['sh000001', 'sz399001', 'sz399006']  # 上证、深证、创业板
            url = f"https://qt.gtimg.cn/q={','.join(indices)}"
            
            response = self.session.get(url, timeout=10)
            response.encoding = 'gbk'
            
            result = {}
            lines = response.text.strip().split('\n')
            index_names = ['上证指数', '深证成指', '创业板指']
            
            for i, line in enumerate(lines):
                if i >= len(index_names):
                    break
                
                if '="' in line and '";' in line:
                    data_part = line.split('="')[1].split('";')[0]
                    if data_part and data_part != 'null':
                        parts = data_part.split('~')
                        if len(parts) >= 10:
                            current = float(parts[3]) if parts[3] else 0
                            pre_close = float(parts[4]) if parts[4] else 0
                            
                            result[index_names[i]] = {
                                'current': current,
                                'change': current - pre_close,
                                'change_pct': ((current - pre_close) / pre_close * 100) if pre_close > 0 else 0,
                                'volume': int(parts[6]) if parts[6] else 0
                            }
            
            return result
            
        except Exception as e:
            print(f"获取市场指数失败: {e}")
            return {}


def demo():
    """演示功能"""
    print("=" * 50)
    print("简化版A股数据客户端演示")
    print("=" * 50)
    
    client = SimpleStockClient()
    
    # 测试股票
    test_stocks = ['000001.SZ', '600000.SH', '600519.SH']
    
    # 1. 获取实时行情
    print("\n=== 实时行情 ===")
    realtime_data = client.get_realtime_quote(test_stocks)
    
    for stock_code, data in realtime_data.items():
        print(f"\n{stock_code} - {data.get('name', 'N/A')}")
        print(f"当前价: {data.get('current', 'N/A'):.2f}")
        print(f"涨跌: {data.get('change', 0):+.2f} ({data.get('change_pct', 0):+.2f}%)")
        print(f"成交量: {data.get('volume', 'N/A'):,}")
        print(f"时间: {data.get('time', 'N/A')}")
    
    # 2. 获取历史数据示例
    print(f"\n=== 历史数据示例 (模拟数据) ===")
    historical_data = client.get_historical_data_simple('000001.SZ', days=5)
    if not historical_data.empty:
        print("平安银行最近5天数据:")
        print(historical_data[['date', 'open', 'high', 'low', 'close', 'volume']].to_string(index=False))
    
    # 3. 获取市场指数
    print(f"\n=== 市场指数 ===")
    indices = client.get_market_indices()
    for name, data in indices.items():
        print(f"{name}: {data.get('current', 'N/A'):.2f} "
              f"({data.get('change', 0):+.2f}, {data.get('change_pct', 0):+.2f}%)")
    
    print("\n" + "=" * 50)
    print("演示完成")


def quick_check(stock_codes):
    """快速查看股票"""
    client = SimpleStockClient()
    data = client.get_realtime_quote(stock_codes)
    
    print(f"{'股票代码':<12} {'名称':<8} {'当前价':<8} {'涨跌幅':<10} {'成交量':<12}")
    print("-" * 60)
    
    for code in stock_codes:
        if code in data:
            info = data[code]
            print(f"{code:<12} {info.get('name', 'N/A'):<8} "
                  f"{info.get('current', 0):<8.2f} "
                  f"{info.get('change_pct', 0):+6.2f}% "
                  f"{info.get('volume', 0):<12,}")
        else:
            print(f"{code:<12} {'N/A':<8} {'N/A':<8} {'N/A':<10} {'N/A':<12}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        stock_codes = sys.argv[1:]
        quick_check(stock_codes)
    else:
        # 演示模式
        demo()
