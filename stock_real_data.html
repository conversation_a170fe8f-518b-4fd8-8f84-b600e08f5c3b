<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股实时监控系统 - 真实数据版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .price-up { background-color: rgba(34, 197, 94, 0.1); }
        .price-down { background-color: rgba(239, 68, 68, 0.1); }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">📊 A股实时监控 <span class="text-sm text-green-600">(真实数据)</span></h1>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div id="dataStatus" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span id="statusText" class="text-sm text-gray-600">准备中...</span>
                    </div>
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="输入股票代码 (如: 000001.SZ)"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 w-64"
                    />
                    <button
                        id="refreshBtn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                        <span id="refreshIcon">🔄</span> 刷新
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 数据源说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <div class="text-blue-600 mr-3">ℹ️</div>
                <div>
                    <h3 class="text-sm font-medium text-blue-800">数据源说明</h3>
                    <p class="text-sm text-blue-700 mt-1">
                        本系统使用Python后端获取腾讯财经API的真实A股数据。
                        <span id="pythonStatus" class="font-medium">正在检测Python后端...</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- 市场指数 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    📈 上证指数
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="sh-current">--</div>
                <div class="text-sm" id="sh-change">加载中...</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    📊 深证成指
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="sz-current">--</div>
                <div class="text-sm" id="sz-change">加载中...</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    🚀 创业板指
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="cy-current">--</div>
                <div class="text-sm" id="cy-change">加载中...</div>
            </div>
        </div>

        <!-- 股票列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">实时行情</h2>
                <p class="text-sm text-gray-500 mt-1">
                    共 <span id="stockCount">0</span> 只股票 • 
                    <span id="updateTime">--</span> • 
                    <span id="refreshStatus">准备获取数据...</span>
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票代码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">当前价</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">涨跌</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">涨跌幅</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">成交量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">更新时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                        </tr>
                    </thead>
                    <tbody id="stockTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 股票数据将在这里生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-12 hidden">
                <div class="text-6xl mb-4">📈</div>
                <p class="text-lg font-medium text-gray-500">暂无股票数据</p>
                <p class="text-sm text-gray-400 mt-2">请在搜索框中添加股票代码</p>
            </div>

            <!-- 错误状态 -->
            <div id="errorState" class="text-center py-12 hidden">
                <div class="text-6xl mb-4">⚠️</div>
                <p class="text-lg font-medium text-red-600">数据获取失败</p>
                <p class="text-sm text-gray-500 mt-2" id="errorMessage">请检查网络连接或稍后重试</p>
                <button onclick="fetchRealData()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    重试
                </button>
            </div>
        </div>
    </main>

    <script>
        console.log('🚀 A股实时监控系统启动 (真实数据版)');

        // 全局变量
        let watchList = ['000001.SZ', '600000.SH', '600519.SH', '000858.SZ', '002415.SZ'];
        let stockData = {};
        let refreshInterval;
        let isLoading = false;

        // 更新状态指示器
        function updateStatus(status, message) {
            const statusDot = document.getElementById('dataStatus');
            const statusText = document.getElementById('statusText');
            
            statusDot.className = `w-3 h-3 rounded-full ${
                status === 'success' ? 'bg-green-500' :
                status === 'loading' ? 'bg-yellow-500 loading' :
                status === 'error' ? 'bg-red-500' : 'bg-gray-400'
            }`;
            
            statusText.textContent = message;
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 100000000) return (num / 100000000).toFixed(1) + '亿';
            if (num >= 10000) return (num / 10000).toFixed(1) + '万';
            return num?.toLocaleString() || '0';
        }

        // 获取真实股票数据
        async function fetchRealData() {
            if (isLoading) return;
            
            console.log('📡 开始获取真实数据...');
            isLoading = true;
            updateStatus('loading', '获取数据中...');
            
            const refreshBtn = document.getElementById('refreshBtn');
            const refreshIcon = document.getElementById('refreshIcon');
            const refreshStatus = document.getElementById('refreshStatus');
            
            refreshBtn.disabled = true;
            refreshIcon.textContent = '⏳';
            refreshStatus.textContent = '正在获取真实数据...';

            try {
                // 调用Python脚本获取真实数据
                const response = await fetch('/api/realtime', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        stock_codes: watchList
                    })
                });

                if (!response.ok) {
                    throw new Error('API请求失败');
                }

                const data = await response.json();
                console.log('✅ 真实数据获取成功:', data);
                
                stockData = data;
                updateStockTable();
                updateStatus('success', '数据已更新');
                refreshStatus.textContent = `最后更新: ${new Date().toLocaleTimeString()}`;
                
                document.getElementById('errorState').classList.add('hidden');
                
            } catch (error) {
                console.error('❌ 获取真实数据失败:', error);
                
                // 显示错误状态
                document.getElementById('errorState').classList.remove('hidden');
                document.getElementById('errorMessage').textContent = 
                    `错误: ${error.message}. 请确保Python后端服务正在运行。`;
                
                updateStatus('error', '数据获取失败');
                refreshStatus.textContent = '数据获取失败，请重试';
                
                // 回退到模拟数据
                console.log('🔄 回退到模拟数据...');
                generateMockData();
            } finally {
                isLoading = false;
                refreshBtn.disabled = false;
                refreshIcon.textContent = '🔄';
            }
        }

        // 生成模拟数据（备用方案）
        function generateMockData() {
            console.log('🎭 生成模拟数据作为备用...');
            
            const stockNames = {
                '000001.SZ': '平安银行',
                '000002.SZ': '万科A',
                '600000.SH': '浦发银行',
                '600036.SH': '招商银行',
                '600519.SH': '贵州茅台',
                '000858.SZ': '五粮液',
                '002415.SZ': '海康威视',
                '300059.SZ': '东方财富',
            };

            const mockData = {};
            watchList.forEach(code => {
                const basePrice = Math.random() * 100 + 10;
                const change = (Math.random() - 0.5) * 10;
                const changePct = (change / basePrice) * 100;
                
                mockData[code] = {
                    name: stockNames[code] || '未知股票',
                    current: parseFloat((basePrice + change).toFixed(2)),
                    change: parseFloat(change.toFixed(2)),
                    change_pct: parseFloat(changePct.toFixed(2)),
                    volume: Math.floor(Math.random() * 10000000),
                    time: new Date().toLocaleTimeString(),
                };
            });

            stockData = mockData;
            updateStockTable();
            updateStatus('error', '使用模拟数据');
            document.getElementById('refreshStatus').textContent = '使用模拟数据 (后端未连接)';
        }

        // 更新股票表格
        function updateStockTable() {
            console.log('📋 更新股票表格');
            const tbody = document.getElementById('stockTableBody');
            const emptyState = document.getElementById('emptyState');
            const stockCount = document.getElementById('stockCount');
            const updateTime = document.getElementById('updateTime');
            
            if (watchList.length === 0) {
                tbody.innerHTML = '';
                emptyState.classList.remove('hidden');
                stockCount.textContent = '0';
                return;
            }

            emptyState.classList.add('hidden');
            stockCount.textContent = watchList.length;
            updateTime.textContent = new Date().toLocaleTimeString();
            tbody.innerHTML = '';

            Object.entries(stockData).forEach(([code, stock]) => {
                const changeColor = stock.change > 0 ? 'text-green-600' : stock.change < 0 ? 'text-red-600' : 'text-gray-500';
                const changeBg = stock.change > 0 ? 'bg-green-50' : stock.change < 0 ? 'bg-red-50' : 'bg-gray-50';
                
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${stock.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold ${changeColor}">¥${stock.current.toFixed(2)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${changeColor}">
                        ${stock.change > 0 ? '📈' : stock.change < 0 ? '📉' : '➖'} ${stock.change > 0 ? '+' : ''}${stock.change.toFixed(2)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${changeBg} ${changeColor}">
                            ${stock.change_pct > 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(stock.volume)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${stock.time || '--'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="removeStock('${code}')" class="text-red-600 hover:text-red-900 px-2 py-1 rounded">
                            ❌
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 添加股票
        function addStock(stockCode) {
            if (!watchList.includes(stockCode)) {
                watchList.push(stockCode);
                console.log('➕ 添加股票:', stockCode);
                fetchRealData();
            }
        }

        // 移除股票
        function removeStock(stockCode) {
            watchList = watchList.filter(code => code !== stockCode);
            delete stockData[stockCode];
            console.log('➖ 移除股票:', stockCode);
            updateStockTable();
        }

        // 事件监听
        document.getElementById('refreshBtn').addEventListener('click', fetchRealData);

        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const stockCode = this.value.trim().toUpperCase();
                if (stockCode) {
                    addStock(stockCode);
                    this.value = '';
                }
            }
        });

        // 初始化
        console.log('🎯 初始化系统');
        document.getElementById('pythonStatus').textContent = '正在尝试连接...';
        
        // 首次获取数据
        fetchRealData();

        // 设置定时刷新 (30秒，避免频繁请求)
        refreshInterval = setInterval(fetchRealData, 30000);
        console.log('⏰ 设置定时刷新: 每30秒');

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                console.log('🧹 清理定时器');
            }
        });

        console.log('✅ A股实时监控系统启动完成 (真实数据版)');
    </script>
</body>
</html>
