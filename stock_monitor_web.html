<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股实时监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        // 确保页面加载完成
        console.log('页面开始加载...');
    </script>
    <style>
        .price-up {
            animation: flash-green 0.5s ease-in-out;
        }
        .price-down {
            animation: flash-red 0.5s ease-in-out;
        }
        @keyframes flash-green {
            0% { background-color: transparent; }
            50% { background-color: rgba(34, 197, 94, 0.2); }
            100% { background-color: transparent; }
        }
        @keyframes flash-red {
            0% { background-color: transparent; }
            50% { background-color: rgba(239, 68, 68, 0.2); }
            100% { background-color: transparent; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i data-lucide="bar-chart-3" class="h-8 w-8 text-blue-600 mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">A股实时监控</h1>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"></i>
                        <input
                            type="text"
                            id="searchInput"
                            placeholder="输入股票代码 (如: 000001.SZ)"
                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                        />
                    </div>
                    
                    <!-- 刷新按钮 -->
                    <button
                        id="refreshBtn"
                        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                        <i data-lucide="refresh-cw" class="h-4 w-4 mr-2"></i>
                        刷新
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 市场指数 -->
        <div id="marketIndices" class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- 指数卡片将在这里动态生成 -->
        </div>

        <!-- 股票列表 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">实时行情</h2>
                <p class="text-sm text-gray-500 mt-1">
                    <span id="stockCount">0</span> 只股票 • 每5秒自动刷新
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股票</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前价</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">涨跌</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">涨跌幅</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成交量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="stockTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 股票数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-12 hidden">
                <i data-lucide="trending-up" class="h-12 w-12 mx-auto mb-4 text-gray-300"></i>
                <p class="text-lg font-medium text-gray-500">暂无股票数据</p>
                <p class="text-sm text-gray-400 mt-2">请在搜索框中添加股票代码</p>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12 hidden">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <span class="mt-3 text-gray-600 block">加载中...</span>
        </div>
    </main>

    <script>
        // 全局变量
        let watchList = ['000001.SZ', '600000.SH', '600519.SH', '000858.SZ', '002415.SZ'];
        let stockData = {};
        let prevPrices = {};
        let refreshInterval;

        // 初始化Lucide图标
        function initIcons() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            } else {
                console.log('Lucide未加载，稍后重试...');
                setTimeout(initIcons, 100);
            }
        }
        initIcons();

        // 格式化数字
        function formatNumber(num) {
            if (num >= 100000000) {
                return (num / 100000000).toFixed(1) + '亿';
            } else if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            }
            return num?.toLocaleString() || '0';
        }

        // 获取颜色类
        function getChangeColor(change) {
            if (change > 0) return 'text-green-600';
            if (change < 0) return 'text-red-600';
            return 'text-gray-500';
        }

        // 获取背景颜色类
        function getChangeBgColor(change) {
            if (change > 0) return 'bg-green-50';
            if (change < 0) return 'bg-red-50';
            return 'bg-gray-50';
        }

        // 模拟获取实时数据
        function getMockRealTimeData(stockCodes) {
            const mockData = {};
            const stockNames = {
                '000001.SZ': '平安银行',
                '000002.SZ': '万科A',
                '600000.SH': '浦发银行',
                '600036.SH': '招商银行',
                '600519.SH': '贵州茅台',
                '000858.SZ': '五粮液',
                '002415.SZ': '海康威视',
                '300059.SZ': '东方财富',
            };

            stockCodes.forEach(code => {
                const basePrice = Math.random() * 100 + 10;
                const change = (Math.random() - 0.5) * 10;
                const changePct = (change / basePrice) * 100;
                
                mockData[code] = {
                    name: stockNames[code] || '未知股票',
                    current: parseFloat((basePrice + change).toFixed(2)),
                    pre_close: parseFloat(basePrice.toFixed(2)),
                    open: parseFloat((basePrice + Math.random() * 2 - 1).toFixed(2)),
                    high: parseFloat((basePrice + Math.abs(change) + Math.random() * 2).toFixed(2)),
                    low: parseFloat((basePrice - Math.abs(change) - Math.random() * 2).toFixed(2)),
                    volume: Math.floor(Math.random() * 10000000),
                    turnover: Math.floor(Math.random() * 1000000000),
                    change: parseFloat(change.toFixed(2)),
                    change_pct: parseFloat(changePct.toFixed(2)),
                    time: new Date().toLocaleTimeString(),
                };
            });

            return mockData;
        }

        // 模拟获取市场指数
        function getMockIndicesData() {
            return {
                '上证指数': {
                    current: 3424.23 + (Math.random() - 0.5) * 50,
                    change: -24.22 + (Math.random() - 0.5) * 20,
                    change_pct: -0.70 + (Math.random() - 0.5) * 2,
                    volume: 234567890,
                },
                '深证成指': {
                    current: 10378.55 + (Math.random() - 0.5) * 200,
                    change: 35.07 + (Math.random() - 0.5) * 30,
                    change_pct: 0.34 + (Math.random() - 0.5) * 1,
                    volume: 345678901,
                },
                '创业板指': {
                    current: 2124.34 + (Math.random() - 0.5) * 100,
                    change: 9.91 + (Math.random() - 0.5) * 15,
                    change_pct: 0.47 + (Math.random() - 0.5) * 1,
                    volume: 123456789,
                },
            };
        }

        // 渲染市场指数
        function renderMarketIndices(indices) {
            const container = document.getElementById('marketIndices');
            container.innerHTML = '';

            Object.entries(indices).forEach(([name, data]) => {
                const changeColor = getChangeColor(data.change);
                const changeBgColor = getChangeBgColor(data.change);
                
                const card = document.createElement('div');
                card.className = `bg-white rounded-lg shadow-sm border-2 p-6 transition-all hover:shadow-md ${changeBgColor.replace('bg-', 'border-').replace('-50', '-200')}`;
                
                card.innerHTML = `
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i data-lucide="activity" class="h-6 w-6 text-blue-600 mr-2"></i>
                            <h3 class="text-lg font-semibold text-gray-900">${name}</h3>
                        </div>
                        <div class="${changeColor}">
                            <i data-lucide="${data.change > 0 ? 'trending-up' : data.change < 0 ? 'trending-down' : 'minus'}" class="h-5 w-5"></i>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-baseline justify-between">
                            <span class="text-2xl font-bold text-gray-900">${data.current.toFixed(2)}</span>
                            <div class="text-right ${changeColor}">
                                <div class="text-sm font-medium">${data.change > 0 ? '+' : ''}${data.change.toFixed(2)}</div>
                                <div class="text-xs">${data.change_pct > 0 ? '+' : ''}${data.change_pct.toFixed(2)}%</div>
                            </div>
                        </div>
                        <div class="pt-2 border-t border-gray-100">
                            <div class="flex justify-between text-sm text-gray-600">
                                <span>成交量</span>
                                <span>${formatNumber(data.volume)}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });

            lucide.createIcons();
        }

        // 渲染股票表格
        function renderStockTable(stocks) {
            const tbody = document.getElementById('stockTableBody');
            const emptyState = document.getElementById('emptyState');
            const stockCount = document.getElementById('stockCount');
            
            if (stocks.length === 0) {
                tbody.innerHTML = '';
                emptyState.classList.remove('hidden');
                stockCount.textContent = '0';
                return;
            }

            emptyState.classList.add('hidden');
            stockCount.textContent = stocks.length;
            tbody.innerHTML = '';

            stocks.forEach(stock => {
                const prevPrice = prevPrices[stock.code];
                let animationClass = '';
                
                if (prevPrice !== undefined && prevPrice !== stock.current) {
                    animationClass = stock.current > prevPrice ? 'price-up' : 'price-down';
                }

                const changeColor = getChangeColor(stock.change);
                const changeBgColor = getChangeBgColor(stock.change_pct);
                
                const row = document.createElement('tr');
                row.className = `hover:bg-gray-50 transition-colors ${animationClass}`;
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${stock.code}</div>
                            <div class="text-sm text-gray-500">${stock.name}</div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-semibold ${changeColor}">¥${stock.current.toFixed(2)}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center text-sm font-medium ${changeColor}">
                            <i data-lucide="${stock.change > 0 ? 'trending-up' : stock.change < 0 ? 'trending-down' : 'minus'}" class="h-4 w-4 mr-1"></i>
                            ${stock.change > 0 ? '+' : ''}${stock.change.toFixed(2)}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${changeBgColor} ${changeColor}">
                            ${stock.change_pct > 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(stock.volume)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${stock.time}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="removeStock('${stock.code}')" class="text-red-600 hover:text-red-900 p-1 rounded" title="移除">
                            <i data-lucide="x" class="h-4 w-4"></i>
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
                
                // 更新前一次价格
                prevPrices[stock.code] = stock.current;
            });

            lucide.createIcons();
        }

        // 获取数据
        async function fetchData() {
            console.log('开始获取数据，监控列表:', watchList);
            const loadingState = document.getElementById('loadingState');
            const refreshBtn = document.getElementById('refreshBtn');

            try {
                if (refreshBtn) {
                    refreshBtn.disabled = true;
                    const icon = refreshBtn.querySelector('i');
                    if (icon) icon.classList.add('animate-spin');
                }

                // 模拟数据获取
                console.log('生成模拟数据...');
                const stockQuotes = getMockRealTimeData(watchList);
                const indices = getMockIndicesData();

                console.log('股票数据:', stockQuotes);
                console.log('指数数据:', indices);

                stockData = stockQuotes;

                const stocks = Object.entries(stockData).map(([code, data]) => ({
                    code,
                    ...data
                }));

                console.log('渲染数据，股票数量:', stocks.length);
                renderMarketIndices(indices);
                renderStockTable(stocks);

            } catch (error) {
                console.error('获取数据失败:', error);
            } finally {
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    const icon = refreshBtn.querySelector('i');
                    if (icon) icon.classList.remove('animate-spin');
                }
                if (loadingState) {
                    loadingState.classList.add('hidden');
                }
            }
        }

        // 添加股票
        function addStock(stockCode) {
            if (!watchList.includes(stockCode)) {
                watchList.push(stockCode);
                fetchData();
            }
        }

        // 移除股票
        function removeStock(stockCode) {
            watchList = watchList.filter(code => code !== stockCode);
            delete stockData[stockCode];
            delete prevPrices[stockCode];
            
            const stocks = Object.entries(stockData).map(([code, data]) => ({
                code,
                ...data
            }));
            
            renderStockTable(stocks);
        }

        // 事件监听
        document.getElementById('refreshBtn').addEventListener('click', fetchData);

        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const stockCode = this.value.trim().toUpperCase();
                if (stockCode) {
                    addStock(stockCode);
                    this.value = '';
                }
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始获取数据...');
            fetchData();

            // 设置定时刷新
            refreshInterval = setInterval(fetchData, 5000);
        });

        // 立即执行一次（防止DOMContentLoaded已经触发）
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOMContentLoaded触发');
                fetchData();
            });
        } else {
            console.log('页面已加载，立即执行');
            fetchData();
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });

        // 强制初始化（备用方案）
        setTimeout(function() {
            console.log('强制初始化执行');
            if (!stockData || Object.keys(stockData).length === 0) {
                console.log('检测到没有数据，强制执行fetchData');
                fetchData();
            }
        }, 1000);
    </script>
</body>
</html>
