# xtdata 实时行情和历史数据获取使用说明

## 前提条件

1. **安装MiniQMT客户端**
   - 下载并安装QMT-投研版或QMT-极简版
   - 启动MiniQMT客户端并保持运行状态

2. **Python环境**
   - Python 3.6+
   - pandas库（用于数据处理）

## 快速开始

### 1. 基本连接测试

```python
# 运行测试脚本，检查连接状态
python test_xtdata.py
```

### 2. 使用示例脚本

```python
# 运行完整示例
python market_data_example.py
```

### 3. 使用简化客户端

```python
from market_data_client import MarketDataClient

# 创建客户端
client = MarketDataClient()

# 连接
if client.connect():
    print("连接成功！")
    
    # 获取股票列表
    stocks = client.get_stock_list('沪深A股')
    
    # 获取实时行情
    realtime_data = client.get_realtime_quote(stocks[:5])
    
    # 获取历史数据
    historical_data = client.get_historical_data(stocks[:3], period='1d', count=30)
    
    # 断开连接
    client.disconnect()
```

## 主要功能

### 1. 实时行情获取

```python
# 获取指定股票的实时行情
stock_codes = ['000001.SZ', '600000.SH', '000002.SZ']
realtime_data = client.get_realtime_quote(stock_codes)

# 返回数据包含：
# - time: 时间戳
# - lastPrice: 最新价
# - volume: 成交量
# - turnover: 成交额
# - openPrice: 开盘价
# - highPrice: 最高价
# - lowPrice: 最低价
# - preClose: 昨收价
# - pctChg: 涨跌幅
```

### 2. 历史数据获取

```python
# 获取历史K线数据
historical_data = client.get_historical_data(
    stock_codes=['000001.SZ', '600000.SH'],
    period='1d',  # 支持: 1m, 5m, 15m, 30m, 1h, 1d
    start_date='20240101',  # 可选，格式YYYYMMDD
    end_date='20241201',    # 可选，格式YYYYMMDD
    count=100  # 当不指定日期时，获取最近N条数据
)

# 返回 {股票代码: DataFrame} 字典
# DataFrame包含: time, open, high, low, close, volume, turnover
```

### 3. 实时数据订阅

```python
def on_data_callback(data):
    print(f"收到实时数据: {data}")

# 订阅实时推送
client.subscribe_quote(
    stock_codes=['000001.SZ'],
    period='1m',
    callback=on_data_callback
)

# 取消订阅
client.unsubscribe_quote(['000001.SZ'], '1m')
```

### 4. Tick数据获取

```python
# 获取逐笔数据
tick_data = client.get_tick_data(['000001.SZ', '600000.SH'])
```

## 支持的数据周期

- **分钟级**: 1m, 5m, 15m, 30m, 60m
- **小时级**: 1h, 2h, 4h
- **日级**: 1d
- **周级**: 1w
- **月级**: 1mon

## 支持的股票市场

- **沪深A股**: 上海证券交易所和深圳证券交易所A股
- **创业板**: 深圳创业板股票
- **科创板**: 上海科创板股票
- **港股通**: 港股通标的股票

## 常见问题

### 1. 连接失败
```
错误: 无法连接xtquant服务，请检查QMT-投研版或QMT-极简版是否开启
```
**解决方案**:
- 确保MiniQMT客户端已启动
- 检查防火墙设置
- 确认端口未被占用

### 2. 数据为空
**可能原因**:
- 股票代码格式错误（应为'000001.SZ'格式）
- 请求的时间范围内无交易数据
- 非交易时间段

### 3. 导入错误
如果遇到导入错误，运行：
```python
python test_xtdata.py
```
检查库是否正常工作。

## 股票代码格式

- **上海A股**: 600000.SH, 601318.SH
- **深圳A股**: 000001.SZ, 000002.SZ  
- **创业板**: 300001.SZ, 300015.SZ
- **科创板**: 688001.SH, 688036.SH

## 注意事项

1. **交易时间**: 数据获取最好在交易时间进行（9:30-11:30, 13:00-15:00）
2. **频率限制**: 避免过于频繁的数据请求
3. **内存管理**: 获取大量历史数据时注意内存使用
4. **异常处理**: 建议在代码中添加适当的异常处理

## 完整示例

参考 `market_data_example.py` 和 `market_data_client.py` 文件中的完整示例代码。
