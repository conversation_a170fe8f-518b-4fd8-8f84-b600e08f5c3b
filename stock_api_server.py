#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据API服务器
为前端提供RESTful API接口
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from simple_stock_client import SimpleStockClient
    from stock_analyzer import StockAnalyzer
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保 simple_stock_client.py 和 stock_analyzer.py 文件存在")
    sys.exit(1)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 初始化客户端
stock_client = SimpleStockClient()
analyzer = StockAnalyzer()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'message': '股票数据API服务正常运行'
    })

@app.route('/api/realtime', methods=['POST'])
def get_realtime_quotes():
    """获取实时行情"""
    try:
        data = request.get_json()
        stock_codes = data.get('stock_codes', [])
        
        if not stock_codes:
            return jsonify({'error': '请提供股票代码列表'}), 400
        
        # 获取实时数据
        quotes = stock_client.get_realtime_quote(stock_codes)
        
        return jsonify(quotes)
    
    except Exception as e:
        return jsonify({'error': f'获取实时行情失败: {str(e)}'}), 500

@app.route('/api/historical', methods=['POST'])
def get_historical_data():
    """获取历史数据"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        days = data.get('days', 30)
        
        if not stock_code:
            return jsonify({'error': '请提供股票代码'}), 400
        
        # 获取历史数据
        historical_data = stock_client.get_historical_data_simple(stock_code, days)
        
        # 转换为前端需要的格式
        result = []
        for _, row in historical_data.iterrows():
            result.append({
                'date': row['date'],
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'volume': int(row['volume'])
            })
        
        return jsonify(result)
    
    except Exception as e:
        return jsonify({'error': f'获取历史数据失败: {str(e)}'}), 500

@app.route('/api/indices', methods=['GET'])
def get_market_indices():
    """获取市场指数"""
    try:
        indices = stock_client.get_market_indices()
        return jsonify(indices)
    
    except Exception as e:
        return jsonify({'error': f'获取市场指数失败: {str(e)}'}), 500

@app.route('/api/analysis/<stock_code>', methods=['GET'])
def get_stock_analysis(stock_code):
    """获取股票技术分析"""
    try:
        analysis = analyzer.analyze_stock(stock_code)
        return jsonify(analysis)
    
    except Exception as e:
        return jsonify({'error': f'获取技术分析失败: {str(e)}'}), 500

@app.route('/api/popular', methods=['GET'])
def get_popular_stocks():
    """获取热门股票列表"""
    try:
        popular_stocks = [
            {'code': '000001.SZ', 'name': '平安银行'},
            {'code': '000002.SZ', 'name': '万科A'},
            {'code': '600000.SH', 'name': '浦发银行'},
            {'code': '600036.SH', 'name': '招商银行'},
            {'code': '600519.SH', 'name': '贵州茅台'},
            {'code': '000858.SZ', 'name': '五粮液'},
            {'code': '002415.SZ', 'name': '海康威视'},
            {'code': '300059.SZ', 'name': '东方财富'},
        ]
        
        return jsonify(popular_stocks)
    
    except Exception as e:
        return jsonify({'error': f'获取热门股票失败: {str(e)}'}), 500

@app.route('/api/search/<keyword>', methods=['GET'])
def search_stocks(keyword):
    """搜索股票"""
    try:
        # 简单的股票搜索功能
        # 实际应用中可以连接股票数据库进行搜索
        stock_dict = {
            '000001': {'code': '000001.SZ', 'name': '平安银行'},
            '000002': {'code': '000002.SZ', 'name': '万科A'},
            '600000': {'code': '600000.SH', 'name': '浦发银行'},
            '600036': {'code': '600036.SH', 'name': '招商银行'},
            '600519': {'code': '600519.SH', 'name': '贵州茅台'},
            '000858': {'code': '000858.SZ', 'name': '五粮液'},
            '002415': {'code': '002415.SZ', 'name': '海康威视'},
            '300059': {'code': '300059.SZ', 'name': '东方财富'},
        }
        
        results = []
        keyword = keyword.upper()
        
        for code, info in stock_dict.items():
            if keyword in code or keyword in info['name']:
                results.append(info)
        
        return jsonify(results)
    
    except Exception as e:
        return jsonify({'error': f'搜索股票失败: {str(e)}'}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'API接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动股票数据API服务器...")
    print("📊 API接口:")
    print("   GET  /api/health          - 健康检查")
    print("   POST /api/realtime        - 获取实时行情")
    print("   POST /api/historical      - 获取历史数据")
    print("   GET  /api/indices         - 获取市场指数")
    print("   GET  /api/analysis/<code> - 获取技术分析")
    print("   GET  /api/popular         - 获取热门股票")
    print("   GET  /api/search/<keyword>- 搜索股票")
    print()
    print("🌐 服务地址: http://localhost:8000")
    print("📱 前端地址: http://localhost:5173")
    print()
    
    try:
        app.run(host='0.0.0.0', port=8000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
