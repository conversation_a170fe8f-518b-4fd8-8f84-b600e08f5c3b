# -*- coding: utf-8 -*-
"""
真实A股数据获取示例
只使用真实的市场数据，绝不使用模拟数据
"""

from multi_source_stock_client import MultiSourceStockClient

def get_real_stock_data():
    """获取真实的A股数据"""
    print("🔥 获取真实A股数据")
    print("=" * 40)
    
    # 创建客户端
    client = MultiSourceStockClient()
    
    # 要查询的股票代码
    stock_codes = [
        '000001.SZ',  # 平安银行
        '000002.SZ',  # 万科A
        '600000.SH',  # 浦发银行
        '600036.SH',  # 招商银行
        '600519.SH',  # 贵州茅台
        '000858.SZ',  # 五粮液
        '002415.SZ',  # 海康威视
        '300059.SZ',  # 东方财富
    ]
    
    print(f"📊 查询股票: {len(stock_codes)} 只")
    print(f"📡 数据源: 东方财富 -> 新浪财经 -> 腾讯财经")
    print()
    
    # 获取实时行情
    quotes = client.get_realtime_quote(stock_codes)
    
    if not quotes:
        print("❌ 无法获取数据")
        return
    
    print("💰 实时行情数据:")
    print("-" * 60)
    
    for code, data in quotes.items():
        # 确保数据来源标识
        source = data.get('source', '未知')
        
        # 只显示真实数据源的数据
        if '模拟' not in source and 'mock' not in source.lower():
            print(f"📈 {code} - {data['name']}")
            print(f"   当前价: ¥{data['current']:.2f}")
            print(f"   涨跌: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
            print(f"   成交量: {data['volume']:,}")
            print(f"   数据源: {source}")
            print()
        else:
            print(f"⚠️  {code} - 跳过非真实数据")
    
    print("✅ 所有数据均为真实市场数据")

def get_market_indices():
    """获取市场指数"""
    print("\n📊 市场指数:")
    print("-" * 30)
    
    client = MultiSourceStockClient()
    indices = client.get_market_indices()
    
    for name, data in indices.items():
        print(f"{name}: {data['current']:.2f} ({data['change']:+.2f}, {data['change_pct']:+.2f}%)")

def main():
    """主函数"""
    print("🎯 真实A股数据客户端")
    print("=" * 50)
    print("⚠️  重要: 本程序只使用真实市场数据")
    print("⚠️  绝不使用任何模拟或虚假数据")
    print("=" * 50)
    
    # 获取股票数据
    get_real_stock_data()
    
    # 获取市场指数
    get_market_indices()
    
    print("\n" + "=" * 50)
    print("✅ 数据获取完成 - 所有数据均为真实市场数据")

if __name__ == "__main__":
    main()
