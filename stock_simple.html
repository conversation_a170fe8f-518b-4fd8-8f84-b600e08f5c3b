<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A股实时监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .price-up { background-color: rgba(34, 197, 94, 0.1); }
        .price-down { background-color: rgba(239, 68, 68, 0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">📊 A股实时监控</h1>
                <div class="flex items-center space-x-4">
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="输入股票代码 (如: 000001.SZ)"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 w-64"
                    />
                    <button
                        id="refreshBtn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        🔄 刷新
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 市场指数 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    📈 上证指数
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="sh-current">3424.23</div>
                <div class="text-sm" id="sh-change">-24.22 (-0.70%)</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    📊 深证成指
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="sz-current">10378.55</div>
                <div class="text-sm" id="sz-change">+35.07 (+0.34%)</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                    🚀 创业板指
                </h3>
                <div class="text-2xl font-bold text-gray-900" id="cy-current">2124.34</div>
                <div class="text-sm" id="cy-change">+9.91 (+0.47%)</div>
            </div>
        </div>

        <!-- 股票列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">实时行情</h2>
                <p class="text-sm text-gray-500 mt-1">
                    共 <span id="stockCount">0</span> 只股票 • 每3秒自动刷新
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票代码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">股票名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">当前价</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">涨跌</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">涨跌幅</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">成交量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                        </tr>
                    </thead>
                    <tbody id="stockTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 股票数据将在这里生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-12 hidden">
                <div class="text-6xl mb-4">📈</div>
                <p class="text-lg font-medium text-gray-500">暂无股票数据</p>
                <p class="text-sm text-gray-400 mt-2">请在搜索框中添加股票代码</p>
            </div>
        </div>
    </main>

    <script>
        console.log('🚀 股票监控系统启动');

        // 全局变量
        let watchList = ['000001.SZ', '600000.SH', '600519.SH', '000858.SZ', '002415.SZ'];
        let stockData = {};
        let refreshInterval;

        // 股票名称映射
        const stockNames = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '600000.SH': '浦发银行',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '000858.SZ': '五粮液',
            '002415.SZ': '海康威视',
            '300059.SZ': '东方财富',
        };

        // 格式化数字
        function formatNumber(num) {
            if (num >= 100000000) return (num / 100000000).toFixed(1) + '亿';
            if (num >= 10000) return (num / 10000).toFixed(1) + '万';
            return num?.toLocaleString() || '0';
        }

        // 生成模拟股票数据
        function generateMockData() {
            console.log('📊 生成模拟数据，监控股票:', watchList);
            const mockData = {};

            watchList.forEach(code => {
                const basePrice = Math.random() * 100 + 10;
                const change = (Math.random() - 0.5) * 10;
                const changePct = (change / basePrice) * 100;
                
                mockData[code] = {
                    name: stockNames[code] || '未知股票',
                    current: parseFloat((basePrice + change).toFixed(2)),
                    change: parseFloat(change.toFixed(2)),
                    change_pct: parseFloat(changePct.toFixed(2)),
                    volume: Math.floor(Math.random() * 10000000),
                    time: new Date().toLocaleTimeString(),
                };
            });

            return mockData;
        }

        // 生成模拟指数数据
        function generateMockIndices() {
            return {
                '上证指数': {
                    current: 3424.23 + (Math.random() - 0.5) * 100,
                    change: -24.22 + (Math.random() - 0.5) * 50,
                    change_pct: -0.70 + (Math.random() - 0.5) * 3,
                },
                '深证成指': {
                    current: 10378.55 + (Math.random() - 0.5) * 300,
                    change: 35.07 + (Math.random() - 0.5) * 80,
                    change_pct: 0.34 + (Math.random() - 0.5) * 2,
                },
                '创业板指': {
                    current: 2124.34 + (Math.random() - 0.5) * 150,
                    change: 9.91 + (Math.random() - 0.5) * 30,
                    change_pct: 0.47 + (Math.random() - 0.5) * 2,
                },
            };
        }

        // 更新市场指数
        function updateIndices() {
            const indices = generateMockIndices();
            
            Object.entries(indices).forEach(([name, data]) => {
                const prefix = name === '上证指数' ? 'sh' : name === '深证成指' ? 'sz' : 'cy';
                const currentEl = document.getElementById(`${prefix}-current`);
                const changeEl = document.getElementById(`${prefix}-change`);
                
                if (currentEl && changeEl) {
                    currentEl.textContent = data.current.toFixed(2);
                    changeEl.textContent = `${data.change > 0 ? '+' : ''}${data.change.toFixed(2)} (${data.change_pct > 0 ? '+' : ''}${data.change_pct.toFixed(2)}%)`;
                    changeEl.className = `text-sm ${data.change > 0 ? 'text-green-600' : 'text-red-600'}`;
                }
            });
        }

        // 更新股票表格
        function updateStockTable() {
            console.log('📋 更新股票表格');
            stockData = generateMockData();
            const tbody = document.getElementById('stockTableBody');
            const emptyState = document.getElementById('emptyState');
            const stockCount = document.getElementById('stockCount');
            
            if (watchList.length === 0) {
                tbody.innerHTML = '';
                emptyState.classList.remove('hidden');
                stockCount.textContent = '0';
                return;
            }

            emptyState.classList.add('hidden');
            stockCount.textContent = watchList.length;
            tbody.innerHTML = '';

            Object.entries(stockData).forEach(([code, stock]) => {
                const changeColor = stock.change > 0 ? 'text-green-600' : stock.change < 0 ? 'text-red-600' : 'text-gray-500';
                const changeBg = stock.change > 0 ? 'bg-green-50' : stock.change < 0 ? 'bg-red-50' : 'bg-gray-50';
                
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${stock.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold ${changeColor}">¥${stock.current.toFixed(2)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${changeColor}">
                        ${stock.change > 0 ? '📈' : stock.change < 0 ? '📉' : '➖'} ${stock.change > 0 ? '+' : ''}${stock.change.toFixed(2)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${changeBg} ${changeColor}">
                            ${stock.change_pct > 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(stock.volume)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="removeStock('${code}')" class="text-red-600 hover:text-red-900 px-2 py-1 rounded">
                            ❌
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 刷新所有数据
        function refreshData() {
            console.log('🔄 刷新数据');
            updateIndices();
            updateStockTable();
            console.log('✅ 数据刷新完成');
        }

        // 添加股票
        function addStock(stockCode) {
            if (!watchList.includes(stockCode)) {
                watchList.push(stockCode);
                console.log('➕ 添加股票:', stockCode);
                refreshData();
            }
        }

        // 移除股票
        function removeStock(stockCode) {
            watchList = watchList.filter(code => code !== stockCode);
            console.log('➖ 移除股票:', stockCode);
            refreshData();
        }

        // 事件监听
        document.getElementById('refreshBtn').addEventListener('click', refreshData);

        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const stockCode = this.value.trim().toUpperCase();
                if (stockCode) {
                    addStock(stockCode);
                    this.value = '';
                }
            }
        });

        // 初始化
        console.log('🎯 初始化系统');
        refreshData();

        // 设置定时刷新
        refreshInterval = setInterval(refreshData, 3000);
        console.log('⏰ 设置定时刷新: 每3秒');

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                console.log('🧹 清理定时器');
            }
        });

        console.log('✅ 股票监控系统启动完成');
    </script>
</body>
</html>
