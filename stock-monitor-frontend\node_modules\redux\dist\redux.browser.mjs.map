{"version": 3, "sources": ["../src/utils/formatProdErrorMessage.ts", "../src/utils/symbol-observable.ts", "../src/utils/actionTypes.ts", "../src/utils/isPlainObject.ts", "../src/createStore.ts", "../src/combineReducers.ts", "../src/bindActionCreators.ts", "../src/compose.ts", "../src/applyMiddleware.ts", "../src/utils/isAction.ts"], "sourcesContent": ["/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}", "declare global {\n  interface SymbolConstructor {\n    readonly observable: symbol;\n  }\n}\nconst $$observable = /* #__PURE__ */(() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\nexport default $$observable;", "/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\n\nconst randomString = () => Math.random().toString(36).substring(7).split('').join('.');\nconst ActionTypes = {\n  INIT: `@@redux/INIT${/* #__PURE__ */randomString()}`,\n  REPLACE: `@@redux/REPLACE${/* #__PURE__ */randomString()}`,\n  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nexport default ActionTypes;", "/**\n * @param obj The object to inspect.\n * @returns True if the argument appears to be a plain object.\n */\nexport default function isPlainObject(obj: any): obj is object {\n  if (typeof obj !== 'object' || obj === null) return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage13 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage12 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage11 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage10 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage9 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage8 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage7 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage6 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage5 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage4 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport $$observable from './utils/symbol-observable';\nimport { Store, StoreEnhancer, Dispatch, Observer, ListenerCallback, UnknownIfNonSpecific } from './types/store';\nimport { Action } from './types/actions';\nimport { Reducer } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport { kindOf } from './utils/kindOf';\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(2) : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = (preloadedState as StoreEnhancer<Ext, StateExt>);\n    preloadedState = undefined;\n  }\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(1) : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n    }\n    return enhancer(createStore)(reducer, (preloadedState as PreloadedState | undefined));\n  }\n  let currentReducer = reducer;\n  let currentState: S | PreloadedState | undefined = (preloadedState as PreloadedState | undefined);\n  let currentListeners: Map<number, ListenerCallback> | null = new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns The current state tree of your application.\n   */\n  function getState(): S {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n    return (currentState as S);\n  }\n\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param listener A callback to be invoked on every dispatch.\n   * @returns A function to remove this change listener.\n   */\n  function subscribe(listener: () => void) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(4) : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n  function dispatch(action: A) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n    }\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage9(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== 'string') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage10(17) : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage11(9) : 'Reducers may not dispatch actions.');\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach(listener => {\n      listener();\n    });\n    return action;\n  }\n\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param nextReducer The reducer for the store to use instead.\n   */\n  function replaceReducer(nextReducer: Reducer<S, A>): void {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage12(10) : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n    }\n    currentReducer = ((nextReducer as unknown) as Reducer<S, A, PreloadedState>);\n\n    // This action has a similar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n    dispatch(({\n      type: ActionTypes.REPLACE\n    } as A));\n  }\n\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer: unknown) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage13(11) : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n        }\n        function observeState() {\n          const observerAsObserver = (observer as Observer<S>);\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [$$observable]() {\n        return this;\n      }\n    };\n  }\n\n  // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n  dispatch(({\n    type: ActionTypes.INIT\n  } as A));\n  const store = (({\n    dispatch: (dispatch as Dispatch<A>),\n    subscribe,\n    getState,\n    replaceReducer,\n    [$$observable]: observable\n  } as unknown) as Store<S, A, StateExt> & Ext);\n  return store;\n}\n\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}>(reducer: Reducer<S, A>, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A, PreloadedState>, preloadedState?: PreloadedState | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext;\nexport function legacy_createStore<S, A extends Action, Ext extends {} = {}, StateExt extends {} = {}, PreloadedState = S>(reducer: Reducer<S, A>, preloadedState?: PreloadedState | StoreEnhancer<Ext, StateExt> | undefined, enhancer?: StoreEnhancer<Ext, StateExt>): Store<S, A, UnknownIfNonSpecific<StateExt>> & Ext {\n  return createStore(reducer, (preloadedState as any), enhancer);\n}", "import { formatProdErrorMessage as _formatProdErrorMessage3 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage2 } from \"src/utils/formatProdErrorMessage\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Action } from './types/actions';\nimport { ActionFromReducersMapObject, PreloadedStateShapeFromReducersMapObject, Reducer, StateFromReducersMapObject } from './types/reducers';\nimport ActionTypes from './utils/actionTypes';\nimport isPlainObject from './utils/isPlainObject';\nimport warning from './utils/warning';\nimport { kindOf } from './utils/kindOf';\nfunction getUnexpectedStateShapeWarningMessage(inputState: object, reducers: {\n  [key: string]: Reducer<any, any, any>;\n}, action: Action, unexpectedKeyCache: {\n  [key: string]: true;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n  if (!isPlainObject(inputState)) {\n    return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following ` + `keys: \"${reducerKeys.join('\", \"')}\"`;\n  }\n  const unexpectedKeys = Object.keys(inputState).filter(key => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n  if (unexpectedKeys.length > 0) {\n    return `Unexpected ${unexpectedKeys.length > 1 ? 'keys' : 'key'} ` + `\"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. ` + `Expected to find one of the known reducer keys instead: ` + `\"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n  }\n}\nfunction assertReducerShape(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key];\n    const initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(12) : `The slice reducer for key \"${key}\" returned undefined during initialization. ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n    }\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(13) : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. ` + `Don't try to handle '${ActionTypes.INIT}' or other actions in \"redux/*\" ` + `namespace. They are considered private. Instead, you must return the ` + `current state for any unknown actions, unless it is undefined, ` + `in which case you must return the initial state, regardless of the ` + `action type. The initial state may not be undefined, but can be null.`);\n    }\n  });\n}\n\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @template S Combined state object type.\n *\n * @param reducers An object whose values correspond to different reducer\n *   functions that need to be combined into one. One handy way to obtain it\n *   is to use `import * as reducers` syntax. The reducers may never\n *   return undefined for any action. Instead, they should return their\n *   initial state if the state passed to them was undefined, and the current\n *   state for any unrecognized action.\n *\n * @returns A reducer function that invokes every reducer inside the passed\n *   object, and builds a state object with the same shape.\n */\nexport default function combineReducers<M>(reducers: M): M[keyof M] extends Reducer<any, any, any> | undefined ? Reducer<StateFromReducersMapObject<M>, ActionFromReducersMapObject<M>, Partial<PreloadedStateShapeFromReducersMapObject<M>>> : never;\nexport default function combineReducers(reducers: {\n  [key: string]: Reducer<any, any, any>;\n}) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers: {\n    [key: string]: Reducer<any, any, any>;\n  } = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(`No reducer provided for key \"${key}\"`);\n      }\n    }\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n\n  // This is used to make sure we don't warn about the same\n  // keys multiple times.\n  let unexpectedKeyCache: {\n    [key: string]: true;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError: unknown;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state: StateFromReducersMapObject<typeof reducers> = {}, action: Action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState: StateFromReducersMapObject<typeof reducers> = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === 'undefined') {\n        const actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(14) : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : '(unknown type)'}, the slice reducer for key \"${key}\" returned undefined. ` + `To ignore an action, you must explicitly return the previous state. ` + `If you want this reducer to hold no value, you can return null instead of undefined.`);\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport { Dispatch } from './types/store';\nimport { ActionCreator, ActionCreatorsMapObject, Action } from './types/actions';\nimport { kindOf } from './utils/kindOf';\nfunction bindActionCreator<A extends Action>(actionCreator: ActionCreator<A>, dispatch: Dispatch<A>) {\n  return function (this: any, ...args: any[]) {\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\n\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use `import * as`\n * syntax. You may also pass a single function.\n *\n * @param dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\nexport default function bindActionCreators<A, C extends ActionCreator<A>>(actionCreator: C, dispatch: Dispatch): C;\nexport default function bindActionCreators<A extends ActionCreator<any>, B extends ActionCreator<any>>(actionCreator: A, dispatch: Dispatch): B;\nexport default function bindActionCreators<A, M extends ActionCreatorsMapObject<A>>(actionCreators: M, dispatch: Dispatch): M;\nexport default function bindActionCreators<M extends ActionCreatorsMapObject, N extends ActionCreatorsMapObject>(actionCreators: M, dispatch: Dispatch): N;\nexport default function bindActionCreators(actionCreators: ActionCreator<any> | ActionCreatorsMapObject, dispatch: Dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(16) : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. ` + `Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n  }\n  const boundActionCreators: ActionCreatorsMapObject = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}", "type Func<T extends any[], R> = (...a: T) => R;\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for the\n * resulting composite function.\n *\n * @param funcs The functions to compose.\n * @returns A function obtained by composing the argument functions from right\n *   to left. For example, `compose(f, g, h)` is identical to doing\n *   `(...args) => f(g(h(...args)))`.\n */\nexport default function compose(): <R>(a: R) => R;\nexport default function compose<F extends Function>(f: F): F;\n\n/* two functions */\nexport default function compose<A, T extends any[], R>(f1: (a: A) => R, f2: Func<T, A>): Func<T, R>;\n\n/* three functions */\nexport default function compose<A, B, T extends any[], R>(f1: (b: B) => R, f2: (a: A) => B, f3: Func<T, A>): Func<T, R>;\n\n/* four functions */\nexport default function compose<A, B, C, T extends any[], R>(f1: (c: C) => R, f2: (b: B) => C, f3: (a: A) => B, f4: Func<T, A>): Func<T, R>;\n\n/* rest */\nexport default function compose<R>(f1: (a: any) => R, ...funcs: Function[]): (...args: any[]) => R;\nexport default function compose<R>(...funcs: Function[]): (...args: any[]) => R;\nexport default function compose(...funcs: Function[]) {\n  if (funcs.length === 0) {\n    // infer the argument type so it is usable in inference down the line\n    return <T,>(arg: T) => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args: any) => a(b(...args)));\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"src/utils/formatProdErrorMessage\";\nimport compose from './compose';\nimport { Middleware, MiddlewareAPI } from './types/middleware';\nimport { StoreEnhancer, Dispatch } from './types/store';\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param middlewares The middleware chain to be applied.\n * @returns A store enhancer applying the middleware.\n *\n * @template Ext Dispatch signature added by a middleware.\n * @template S The type of the state supported by a middleware.\n */\nexport default function applyMiddleware(): StoreEnhancer;\nexport default function applyMiddleware<Ext1, S>(middleware1: Middleware<Ext1, S, any>): StoreEnhancer<{\n  dispatch: Ext1;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4;\n}>;\nexport default function applyMiddleware<Ext1, Ext2, Ext3, Ext4, Ext5, S>(middleware1: Middleware<Ext1, S, any>, middleware2: Middleware<Ext2, S, any>, middleware3: Middleware<Ext3, S, any>, middleware4: Middleware<Ext4, S, any>, middleware5: Middleware<Ext5, S, any>): StoreEnhancer<{\n  dispatch: Ext1 & Ext2 & Ext3 & Ext4 & Ext5;\n}>;\nexport default function applyMiddleware<Ext, S = any>(...middlewares: Middleware<any, S, any>[]): StoreEnhancer<{\n  dispatch: Ext;\n}>;\nexport default function applyMiddleware(...middlewares: Middleware[]): StoreEnhancer<any> {\n  return createStore => (reducer, preloadedState) => {\n    const store = createStore(reducer, preloadedState);\n    let dispatch: Dispatch = () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n    };\n    const middlewareAPI: MiddlewareAPI = {\n      getState: store.getState,\n      dispatch: (action, ...args) => dispatch(action, ...args)\n    };\n    const chain = middlewares.map(middleware => middleware(middlewareAPI));\n    dispatch = compose<typeof dispatch>(...chain)(store.dispatch);\n    return {\n      ...store,\n      dispatch\n    };\n  };\n}", "import { Action } from '../types/actions';\nimport isPlainObject from './isPlainObject';\nexport default function isAction(action: unknown): action is Action<string> {\n  return isPlainObject(action) && 'type' in action && typeof (action as Record<'type', unknown>).type === 'string';\n}"], "mappings": "AAOO,SAASA,EAAuBC,EAAc,CACnD,MAAO,yBAAyBA,CAAI,4CAA4CA,CAAI,iFACtF,CCJA,IAAMC,EAAqC,OAAO,QAAW,YAAc,OAAO,YAAc,eACzFC,EAAQD,ECCf,IAAME,EAAe,IAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,EAC/EC,EAAc,CAClB,KAAM,eAA8BD,EAAa,CAAC,GAClD,QAAS,kBAAiCA,EAAa,CAAC,GACxD,qBAAsB,IAAM,+BAA+BA,EAAa,CAAC,EAC3E,EACOE,EAAQD,ECTA,SAARE,EAA+BC,EAAyB,CAC7D,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,KAAM,MAAO,GACpD,IAAIC,EAAQD,EACZ,KAAO,OAAO,eAAeC,CAAK,IAAM,MACtCA,EAAQ,OAAO,eAAeA,CAAK,EAErC,OAAO,OAAO,eAAeD,CAAG,IAAMC,GAAS,OAAO,eAAeD,CAAG,IAAM,IAChF,CC8DO,SAASE,EAAoGC,EAAwCC,EAA4EC,EAA4F,CAClU,GAAI,OAAOF,GAAY,WACrB,MAAM,IAAI,MAA8CG,EAAwB,CAAC,CAAyF,EAE5K,GAAI,OAAOF,GAAmB,YAAc,OAAOC,GAAa,YAAc,OAAOA,GAAa,YAAc,OAAO,UAAU,CAAC,GAAM,WACtI,MAAM,IAAI,MAA8CC,EAAyB,CAAC,CAAgR,EAMpW,GAJI,OAAOF,GAAmB,YAAc,OAAOC,EAAa,MAC9DA,EAAYD,EACZA,EAAiB,QAEf,OAAOC,EAAa,IAAa,CACnC,GAAI,OAAOA,GAAa,WACtB,MAAM,IAAI,MAA8CC,EAAyB,CAAC,CAAsF,EAE1K,OAAOD,EAASH,CAAW,EAAEC,EAAUC,CAA6C,CACtF,CACA,IAAIG,EAAiBJ,EACjBK,EAAgDJ,EAChDK,EAAyD,IAAI,IAC7DC,EAAgBD,EAChBE,EAAoB,EACpBC,EAAgB,GASpB,SAASC,GAA+B,CAClCH,IAAkBD,IACpBC,EAAgB,IAAI,IACpBD,EAAiB,QAAQ,CAACK,EAAUC,IAAQ,CAC1CL,EAAc,IAAIK,EAAKD,CAAQ,CACjC,CAAC,EAEL,CAOA,SAASE,GAAc,CACrB,GAAIJ,EACF,MAAM,IAAI,MAA8CN,EAAyB,CAAC,CAAoN,EAExS,OAAQE,CACV,CAyBA,SAASS,EAAUH,EAAsB,CACvC,GAAI,OAAOA,GAAa,WACtB,MAAM,IAAI,MAA8CR,EAAyB,CAAC,CAAsF,EAE1K,GAAIM,EACF,MAAM,IAAI,MAA8CN,EAAyB,CAAC,CAAoU,EAExZ,IAAIY,EAAe,GACnBL,EAA6B,EAC7B,IAAMM,EAAaR,IACnB,OAAAD,EAAc,IAAIS,EAAYL,CAAQ,EAC/B,UAAuB,CAC5B,GAAKI,EAGL,IAAIN,EACF,MAAM,IAAI,MAA8CN,EAAyB,CAAC,CAA+J,EAEnPY,EAAe,GACfL,EAA6B,EAC7BH,EAAc,OAAOS,CAAU,EAC/BV,EAAmB,KACrB,CACF,CA2BA,SAASW,EAASC,EAAW,CAC3B,GAAI,CAACC,EAAcD,CAAM,EACvB,MAAM,IAAI,MAA8Cf,EAAyB,CAAC,CAA+Z,EAEnf,GAAI,OAAOe,EAAO,KAAS,IACzB,MAAM,IAAI,MAA8Cf,EAAyB,CAAC,CAAgH,EAEpM,GAAI,OAAOe,EAAO,MAAS,SACzB,MAAM,IAAI,MAA8Cf,EAA0B,EAAE,CAAgJ,EAEtO,GAAIM,EACF,MAAM,IAAI,MAA8CN,EAA0B,CAAC,CAAwC,EAE7H,GAAI,CACFM,EAAgB,GAChBJ,EAAeD,EAAeC,EAAca,CAAM,CACpD,QAAE,CACAT,EAAgB,EAClB,CAEA,OADkBH,EAAmBC,GAC3B,QAAQI,GAAY,CAC5BA,EAAS,CACX,CAAC,EACMO,CACT,CAWA,SAASE,EAAeC,EAAkC,CACxD,GAAI,OAAOA,GAAgB,WACzB,MAAM,IAAI,MAA8ClB,EAA0B,EAAE,CAA2F,EAEjLC,EAAmBiB,EAMnBJ,EAAU,CACR,KAAMK,EAAY,OACpB,CAAO,CACT,CAQA,SAASC,GAAa,CACpB,IAAMC,EAAiBV,EACvB,MAAO,CASL,UAAUW,EAAmB,CAC3B,GAAI,OAAOA,GAAa,UAAYA,IAAa,KAC/C,MAAM,IAAI,MAA8CtB,EAA0B,EAAE,CAAqF,EAE3K,SAASuB,GAAe,CACtB,IAAMC,EAAsBF,EACxBE,EAAmB,MACrBA,EAAmB,KAAKd,EAAS,CAAC,CAEtC,CACA,OAAAa,EAAa,EAEN,CACL,YAFkBF,EAAeE,CAAY,CAG/C,CACF,EACA,CAACE,CAAY,GAAI,CACf,OAAO,IACT,CACF,CACF,CAKA,OAAAX,EAAU,CACR,KAAMK,EAAY,IACpB,CAAO,EACS,CACd,SAAWL,EACX,UAAAH,EACA,SAAAD,EACA,eAAAO,EACA,CAACQ,CAAY,EAAGL,CAClB,CAEF,CAgEO,SAASM,EAA2G7B,EAAwBC,EAA4EC,EAA4F,CACzT,OAAOH,EAAYC,EAAUC,EAAwBC,CAAQ,CAC/D,CChVA,SAAS4B,EAAmBC,EAEzB,CACD,OAAO,KAAKA,CAAQ,EAAE,QAAQC,GAAO,CACnC,IAAMC,EAAUF,EAASC,CAAG,EAI5B,GAAI,OAHiBC,EAAQ,OAAW,CACtC,KAAMC,EAAY,IACpB,CAAC,EAC2B,IAC1B,MAAM,IAAI,MAA8CC,EAAwB,EAAE,CAAuU,EAE3Z,GAAI,OAAOF,EAAQ,OAAW,CAC5B,KAAMC,EAAY,qBAAqB,CACzC,CAAC,EAAM,IACL,MAAM,IAAI,MAA8CC,EAAyB,EAAE,CAAyc,CAEhiB,CAAC,CACH,CAqBe,SAARC,EAAiCL,EAErC,CACD,IAAMM,EAAc,OAAO,KAAKN,CAAQ,EAClCO,EAEF,CAAC,EACL,QAASC,EAAI,EAAGA,EAAIF,EAAY,OAAQE,IAAK,CAC3C,IAAMP,EAAMK,EAAYE,CAAC,EAMrB,OAAOR,EAASC,CAAG,GAAM,aAC3BM,EAAcN,CAAG,EAAID,EAASC,CAAG,EAErC,CACA,IAAMQ,EAAmB,OAAO,KAAKF,CAAa,EAI9CG,EAMAC,EACJ,GAAI,CACFZ,EAAmBQ,CAAa,CAClC,OAASK,EAAG,CACVD,EAAsBC,CACxB,CACA,OAAO,SAAqBC,EAAqD,CAAC,EAAGC,EAAgB,CACnG,GAAIH,EACF,MAAMA,EAQR,IAAII,EAAa,GACXC,EAAyD,CAAC,EAChE,QAASR,EAAI,EAAGA,EAAIC,EAAiB,OAAQD,IAAK,CAChD,IAAMP,EAAMQ,EAAiBD,CAAC,EACxBN,EAAUK,EAAcN,CAAG,EAC3BgB,EAAsBJ,EAAMZ,CAAG,EAC/BiB,EAAkBhB,EAAQe,EAAqBH,CAAM,EAC3D,GAAI,OAAOI,EAAoB,IAAa,CAC1C,IAAMC,EAAaL,GAAUA,EAAO,KACpC,MAAM,IAAI,MAA8CV,EAAyB,EAAE,CAAgU,CACrZ,CACAY,EAAUf,CAAG,EAAIiB,EACjBH,EAAaA,GAAcG,IAAoBD,CACjD,CACA,OAAAF,EAAaA,GAAcN,EAAiB,SAAW,OAAO,KAAKI,CAAK,EAAE,OACnEE,EAAaC,EAAYH,CAClC,CACF,CC9HA,SAASO,EAAoCC,EAAiCC,EAAuB,CACnG,OAAO,YAAwBC,EAAa,CAC1C,OAAOD,EAASD,EAAc,MAAM,KAAME,CAAI,CAAC,CACjD,CACF,CA2Be,SAARC,EAAoCC,EAA8DH,EAAoB,CAC3H,GAAI,OAAOG,GAAmB,WAC5B,OAAOL,EAAkBK,EAAgBH,CAAQ,EAEnD,GAAI,OAAOG,GAAmB,UAAYA,IAAmB,KAC3D,MAAM,IAAI,MAA8CC,EAAwB,EAAE,CAA2M,EAE/R,IAAMC,EAA+C,CAAC,EACtD,QAAWC,KAAOH,EAAgB,CAChC,IAAMJ,EAAgBI,EAAeG,CAAG,EACpC,OAAOP,GAAkB,aAC3BM,EAAoBC,CAAG,EAAIR,EAAkBC,EAAeC,CAAQ,EAExE,CACA,OAAOK,CACT,CCvBe,SAARE,KAA4BC,EAAmB,CACpD,OAAIA,EAAM,SAAW,EAEPC,GAAWA,EAErBD,EAAM,SAAW,EACZA,EAAM,CAAC,EAETA,EAAM,OAAO,CAACE,EAAGC,IAAM,IAAIC,IAAcF,EAAEC,EAAE,GAAGC,CAAI,CAAC,CAAC,CAC/D,CCOe,SAARC,KAAoCC,EAA+C,CACxF,OAAOC,GAAe,CAACC,EAASC,IAAmB,CACjD,IAAMC,EAAQH,EAAYC,EAASC,CAAc,EAC7CE,EAAqB,IAAM,CAC7B,MAAM,IAAI,MAA8CC,EAAwB,EAAE,CAAiI,CACrN,EACMC,EAA+B,CACnC,SAAUH,EAAM,SAChB,SAAU,CAACI,KAAWC,IAASJ,EAASG,EAAQ,GAAGC,CAAI,CACzD,EACMC,EAAQV,EAAY,IAAIW,GAAcA,EAAWJ,CAAa,CAAC,EACrE,OAAAF,EAAWO,EAAyB,GAAGF,CAAK,EAAEN,EAAM,QAAQ,EACrD,CACL,GAAGA,EACH,SAAAC,CACF,CACF,CACF,CC1De,SAARQ,EAA0BC,EAA2C,CAC1E,OAAOC,EAAcD,CAAM,GAAK,SAAUA,GAAU,OAAQA,EAAmC,MAAS,QAC1G", "names": ["formatProdErrorMessage", "code", "$$observable", "symbol_observable_default", "randomString", "ActionTypes", "actionTypes_default", "isPlainObject", "obj", "proto", "createStore", "reducer", "preloadedState", "enhancer", "formatProdErrorMessage", "currentReducer", "currentState", "currentListeners", "nextListeners", "listenerIdCounter", "isDispatching", "ensureCanMutateNextListeners", "listener", "key", "getState", "subscribe", "isSubscribed", "listenerId", "dispatch", "action", "isPlainObject", "replaceReducer", "nextReducer", "actionTypes_default", "observable", "outerSubscribe", "observer", "observeState", "observerAsObserver", "symbol_observable_default", "legacy_createStore", "assertReducerShape", "reducers", "key", "reducer", "actionTypes_default", "formatProdErrorMessage", "combineReducers", "reducerKeys", "finalReducers", "i", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shapeAssertionError", "e", "state", "action", "has<PERSON><PERSON>ed", "nextState", "previousStateForKey", "nextStateForKey", "actionType", "bindActionCreator", "actionCreator", "dispatch", "args", "bindActionCreators", "actionCreators", "formatProdErrorMessage", "boundActionCreators", "key", "compose", "funcs", "arg", "a", "b", "args", "applyMiddleware", "middlewares", "createStore", "reducer", "preloadedState", "store", "dispatch", "formatProdErrorMessage", "middlewareAPI", "action", "args", "chain", "middleware", "compose", "isAction", "action", "isPlainObject"]}