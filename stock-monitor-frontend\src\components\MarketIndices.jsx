import { TrendingUp, TrendingDown, Activity } from 'lucide-react'

const MarketIndices = ({ indices }) => {
  const getChangeColor = (change) => {
    if (change > 0) return 'text-stock-green'
    if (change < 0) return 'text-stock-red'
    return 'text-gray-500'
  }

  const getChangeBgColor = (change) => {
    if (change > 0) return 'bg-green-50 border-green-200'
    if (change < 0) return 'bg-red-50 border-red-200'
    return 'bg-gray-50 border-gray-200'
  }

  const formatNumber = (num) => {
    if (num >= 100000000) {
      return (num / 100000000).toFixed(1) + '亿'
    } else if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    }
    return num?.toLocaleString() || '0'
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.entries(indices).map(([name, data]) => (
        <div 
          key={name}
          className={`bg-white rounded-lg shadow-sm border-2 p-6 transition-all hover:shadow-md ${getChangeBgColor(data.change)}`}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Activity className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
            </div>
            <div className={`flex items-center ${getChangeColor(data.change)}`}>
              {data.change > 0 ? (
                <TrendingUp className="h-5 w-5" />
              ) : data.change < 0 ? (
                <TrendingDown className="h-5 w-5" />
              ) : null}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-baseline justify-between">
              <span className="text-2xl font-bold text-gray-900">
                {data.current?.toFixed(2)}
              </span>
              <div className={`text-right ${getChangeColor(data.change)}`}>
                <div className="text-sm font-medium">
                  {data.change > 0 ? '+' : ''}{data.change?.toFixed(2)}
                </div>
                <div className="text-xs">
                  {data.change_pct > 0 ? '+' : ''}{data.change_pct?.toFixed(2)}%
                </div>
              </div>
            </div>

            <div className="pt-2 border-t border-gray-100">
              <div className="flex justify-between text-sm text-gray-600">
                <span>成交量</span>
                <span>{formatNumber(data.volume)}</span>
              </div>
            </div>
          </div>

          {/* 简单的趋势指示器 */}
          <div className="mt-4 flex items-center justify-center">
            <div className="flex space-x-1">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className={`h-1 w-8 rounded-full ${
                    data.change_pct > 0 
                      ? 'bg-green-200' 
                      : data.change_pct < 0 
                        ? 'bg-red-200' 
                        : 'bg-gray-200'
                  }`}
                  style={{
                    opacity: Math.abs(data.change_pct) > i * 0.5 ? 1 : 0.3
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      ))}

      {Object.keys(indices).length === 0 && (
        <div className="col-span-full bg-white rounded-lg shadow-sm p-8">
          <div className="text-center text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">市场指数加载中...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default MarketIndices
