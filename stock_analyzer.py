# -*- coding: utf-8 -*-
"""
A股历史数据分析工具
提供技术指标计算和数据分析功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from a_stock_data_client import AStockDataClient


class StockAnalyzer:
    """股票数据分析器"""
    
    def __init__(self):
        self.client = AStockDataClient()
    
    def get_stock_data(self, stock_code, days=100):
        """
        获取股票数据
        
        Args:
            stock_code: 股票代码
            days: 获取天数
            
        Returns:
            DataFrame: 股票数据
        """
        return self.client.get_historical_data(stock_code, count=days)
    
    def calculate_ma(self, data, periods=[5, 10, 20, 60]):
        """
        计算移动平均线
        
        Args:
            data: 股票数据DataFrame
            periods: 周期列表
            
        Returns:
            DataFrame: 包含移动平均线的数据
        """
        df = data.copy()
        
        for period in periods:
            df[f'MA{period}'] = df['close'].rolling(window=period).mean()
        
        return df
    
    def calculate_rsi(self, data, period=14):
        """
        计算RSI相对强弱指标
        
        Args:
            data: 股票数据DataFrame
            period: 计算周期
            
        Returns:
            Series: RSI值
        """
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_macd(self, data, fast=12, slow=26, signal=9):
        """
        计算MACD指标
        
        Args:
            data: 股票数据DataFrame
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            DataFrame: MACD相关数据
        """
        exp1 = data['close'].ewm(span=fast).mean()
        exp2 = data['close'].ewm(span=slow).mean()
        
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        
        return pd.DataFrame({
            'MACD': macd,
            'Signal': signal_line,
            'Histogram': histogram
        })
    
    def calculate_bollinger_bands(self, data, period=20, std_dev=2):
        """
        计算布林带
        
        Args:
            data: 股票数据DataFrame
            period: 周期
            std_dev: 标准差倍数
            
        Returns:
            DataFrame: 布林带数据
        """
        ma = data['close'].rolling(window=period).mean()
        std = data['close'].rolling(window=period).std()
        
        upper_band = ma + (std * std_dev)
        lower_band = ma - (std * std_dev)
        
        return pd.DataFrame({
            'Middle': ma,
            'Upper': upper_band,
            'Lower': lower_band
        })
    
    def analyze_stock(self, stock_code, days=100):
        """
        综合分析股票
        
        Args:
            stock_code: 股票代码
            days: 分析天数
            
        Returns:
            dict: 分析结果
        """
        print(f"正在分析 {stock_code}...")
        
        # 获取数据
        data = self.get_stock_data(stock_code, days)
        if data.empty:
            return {"error": "无法获取数据"}
        
        # 基本信息
        latest = data.iloc[-1]
        prev = data.iloc[-2] if len(data) > 1 else latest
        
        # 计算技术指标
        data_with_ma = self.calculate_ma(data)
        rsi = self.calculate_rsi(data)
        macd_data = self.calculate_macd(data)
        bb_data = self.calculate_bollinger_bands(data)
        
        # 价格分析
        price_change = latest['close'] - prev['close']
        price_change_pct = (price_change / prev['close']) * 100
        
        # 趋势分析
        ma5 = data_with_ma['MA5'].iloc[-1] if not pd.isna(data_with_ma['MA5'].iloc[-1]) else 0
        ma20 = data_with_ma['MA20'].iloc[-1] if not pd.isna(data_with_ma['MA20'].iloc[-1]) else 0
        
        trend = "上升" if latest['close'] > ma5 > ma20 else "下降" if latest['close'] < ma5 < ma20 else "震荡"
        
        # RSI分析
        current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
        rsi_signal = "超买" if current_rsi > 70 else "超卖" if current_rsi < 30 else "正常"
        
        # MACD分析
        current_macd = macd_data['MACD'].iloc[-1] if not pd.isna(macd_data['MACD'].iloc[-1]) else 0
        current_signal = macd_data['Signal'].iloc[-1] if not pd.isna(macd_data['Signal'].iloc[-1]) else 0
        macd_signal = "金叉" if current_macd > current_signal else "死叉"
        
        # 布林带分析
        current_price = latest['close']
        bb_upper = bb_data['Upper'].iloc[-1] if not pd.isna(bb_data['Upper'].iloc[-1]) else current_price
        bb_lower = bb_data['Lower'].iloc[-1] if not pd.isna(bb_data['Lower'].iloc[-1]) else current_price
        
        if current_price > bb_upper:
            bb_position = "上轨上方(可能超买)"
        elif current_price < bb_lower:
            bb_position = "下轨下方(可能超卖)"
        else:
            bb_position = "正常区间"
        
        # 成交量分析
        avg_volume = data['volume'].tail(20).mean()
        volume_ratio = latest['volume'] / avg_volume if avg_volume > 0 else 1
        volume_signal = "放量" if volume_ratio > 1.5 else "缩量" if volume_ratio < 0.5 else "正常"
        
        return {
            "stock_code": stock_code,
            "stock_name": latest.get('name', 'N/A'),
            "analysis_date": latest['date'].strftime('%Y-%m-%d'),
            "price_info": {
                "current_price": latest['close'],
                "price_change": price_change,
                "price_change_pct": price_change_pct,
                "high_52w": data['high'].max(),
                "low_52w": data['low'].min(),
            },
            "technical_analysis": {
                "trend": trend,
                "ma5": ma5,
                "ma20": ma20,
                "rsi": current_rsi,
                "rsi_signal": rsi_signal,
                "macd_signal": macd_signal,
                "bollinger_position": bb_position,
            },
            "volume_analysis": {
                "current_volume": latest['volume'],
                "avg_volume_20d": avg_volume,
                "volume_ratio": volume_ratio,
                "volume_signal": volume_signal,
            }
        }
    
    def compare_stocks(self, stock_codes, days=60):
        """
        比较多只股票
        
        Args:
            stock_codes: 股票代码列表
            days: 比较天数
            
        Returns:
            DataFrame: 比较结果
        """
        results = []
        
        for stock_code in stock_codes:
            analysis = self.analyze_stock(stock_code, days)
            if "error" not in analysis:
                results.append({
                    "股票代码": analysis["stock_code"],
                    "股票名称": analysis.get("stock_name", "N/A"),
                    "当前价": analysis["price_info"]["current_price"],
                    "涨跌幅%": analysis["price_info"]["price_change_pct"],
                    "趋势": analysis["technical_analysis"]["trend"],
                    "RSI": analysis["technical_analysis"]["rsi"],
                    "RSI信号": analysis["technical_analysis"]["rsi_signal"],
                    "MACD信号": analysis["technical_analysis"]["macd_signal"],
                    "成交量信号": analysis["volume_analysis"]["volume_signal"],
                })
        
        return pd.DataFrame(results)
    
    def print_analysis_report(self, analysis):
        """打印分析报告"""
        if "error" in analysis:
            print(f"分析失败: {analysis['error']}")
            return
        
        print("=" * 60)
        print(f"股票分析报告 - {analysis['stock_code']} {analysis.get('stock_name', '')}")
        print("=" * 60)
        print(f"分析日期: {analysis['analysis_date']}")
        
        print(f"\n【价格信息】")
        price = analysis['price_info']
        print(f"当前价格: {price['current_price']:.2f}")
        print(f"涨跌: {price['price_change']:+.2f} ({price['price_change_pct']:+.2f}%)")
        print(f"52周最高: {price['high_52w']:.2f}")
        print(f"52周最低: {price['low_52w']:.2f}")
        
        print(f"\n【技术分析】")
        tech = analysis['technical_analysis']
        print(f"趋势: {tech['trend']}")
        print(f"MA5: {tech['ma5']:.2f}")
        print(f"MA20: {tech['ma20']:.2f}")
        print(f"RSI: {tech['rsi']:.2f} ({tech['rsi_signal']})")
        print(f"MACD: {tech['macd_signal']}")
        print(f"布林带位置: {tech['bollinger_position']}")
        
        print(f"\n【成交量分析】")
        vol = analysis['volume_analysis']
        print(f"当前成交量: {vol['current_volume']:,}")
        print(f"20日平均成交量: {vol['avg_volume_20d']:,.0f}")
        print(f"量比: {vol['volume_ratio']:.2f} ({vol['volume_signal']})")


def main():
    """主函数"""
    analyzer = StockAnalyzer()
    
    while True:
        print("\n" + "=" * 50)
        print("A股技术分析工具")
        print("=" * 50)
        print("1. 单股分析")
        print("2. 多股比较")
        print("3. 热门股票分析")
        print("4. 退出")
        
        choice = input("\n请选择功能 (1-4): ").strip()
        
        if choice == '1':
            stock_code = input("请输入股票代码 (如 000001.SZ): ").strip()
            if stock_code:
                analysis = analyzer.analyze_stock(stock_code)
                analyzer.print_analysis_report(analysis)
        
        elif choice == '2':
            stock_input = input("请输入股票代码，用逗号分隔: ").strip()
            if stock_input:
                stock_codes = [code.strip() for code in stock_input.split(',')]
                comparison = analyzer.compare_stocks(stock_codes)
                print("\n股票比较结果:")
                print(comparison.to_string(index=False))
        
        elif choice == '3':
            hot_stocks = ['000001.SZ', '600000.SH', '600519.SH', '000858.SZ', '002415.SZ']
            print("分析热门股票...")
            comparison = analyzer.compare_stocks(hot_stocks)
            print("\n热门股票分析结果:")
            print(comparison.to_string(index=False))
        
        elif choice == '4':
            print("再见!")
            break
        
        else:
            print("无效选择")


if __name__ == "__main__":
    main()
