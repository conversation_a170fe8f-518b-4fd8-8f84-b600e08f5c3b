# 中国A股数据获取工具

## 🎯 项目简介

这是一个专门为获取中国A股实时行情和历史数据而设计的Python工具包，**无需安装MiniQMT客户端**，基于免费的财经API接口。

## ✨ 主要特点

- ✅ **无需额外客户端** - 不需要安装MiniQMT或其他交易软件
- ✅ **实时行情数据** - 获取A股实时价格、涨跌幅、成交量等
- ✅ **历史数据支持** - 获取历史K线数据用于分析
- ✅ **技术指标计算** - 内置MA、RSI、MACD、布林带等技术指标
- ✅ **实时监控工具** - 可视化实时行情监控界面
- ✅ **简单易用** - 简洁的API设计，易于集成

## 📦 文件说明

### 核心文件
- **`simple_stock_client.py`** - 简化版数据客户端（推荐使用）
- **`a_stock_data_client.py`** - 完整版数据客户端
- **`stock_monitor.py`** - 实时行情监控工具
- **`stock_analyzer.py`** - 技术分析工具

### 示例文件
- **`market_data_example.py`** - 完整使用示例
- **`market_data_client.py`** - 封装的客户端类

## 🚀 快速开始

### 1. 基本使用

```python
from simple_stock_client import SimpleStockClient

# 创建客户端
client = SimpleStockClient()

# 获取实时行情
stocks = ['000001.SZ', '600000.SH', '600519.SH']
realtime_data = client.get_realtime_quote(stocks)

for stock_code, data in realtime_data.items():
    print(f"{stock_code}: {data['current']:.2f} ({data['change_pct']:+.2f}%)")
```

### 2. 实时监控

```bash
# 监控指定股票
python stock_monitor.py 000001.SZ 600000.SH

# 交互式监控
python stock_monitor.py
```

### 3. 技术分析

```python
from stock_analyzer import StockAnalyzer

analyzer = StockAnalyzer()
analysis = analyzer.analyze_stock('000001.SZ')
analyzer.print_analysis_report(analysis)
```

## 📊 功能详解

### 实时行情数据

支持获取以下实时数据：
- 当前价格
- 涨跌额和涨跌幅
- 开盘价、最高价、最低价
- 成交量和成交额
- 实时更新时间

```python
# 获取实时行情
client = SimpleStockClient()
data = client.get_realtime_quote(['000001.SZ'])

stock_info = data['000001.SZ']
print(f"当前价: {stock_info['current']}")
print(f"涨跌幅: {stock_info['change_pct']:.2f}%")
print(f"成交量: {stock_info['volume']:,}")
```

### 历史数据获取

```python
# 获取历史数据
historical_data = client.get_historical_data_simple('000001.SZ', days=30)
print(historical_data[['date', 'open', 'high', 'low', 'close', 'volume']])
```

### 市场指数

```python
# 获取主要指数
indices = client.get_market_indices()
for name, data in indices.items():
    print(f"{name}: {data['current']:.2f} ({data['change_pct']:+.2f}%)")
```

## 🔧 技术指标

内置常用技术指标计算：

- **移动平均线 (MA)**: 5日、10日、20日、60日
- **相对强弱指标 (RSI)**: 判断超买超卖
- **MACD**: 趋势跟踪指标
- **布林带**: 价格通道指标

```python
from stock_analyzer import StockAnalyzer

analyzer = StockAnalyzer()

# 单股分析
analysis = analyzer.analyze_stock('000001.SZ')

# 多股比较
comparison = analyzer.compare_stocks(['000001.SZ', '600000.SH'])
```

## 📈 实时监控

### 命令行监控
```bash
# 快速查看
python simple_stock_client.py 000001.SZ 600000.SH

# 实时监控
python stock_monitor.py
```

### 监控界面示例
```
================================================================================
A股实时行情监控 - 2025-06-29 16:14:32
================================================================================
股票代码       股票名称     当前价    涨跌            成交量       更新时间    
--------------------------------------------------------------------------------
000001.SZ    平安银行     12.20    -0.21 (-1.69%)  2,377,600   20250627161421
600000.SH    浦发银行     13.55    -0.07 (-0.51%)  1,451,127   20250627161426
600519.SH    贵州茅台     1403.09  -16.91 (-1.19%) 38,250      20250627161429
```

## 📋 支持的股票代码格式

- **上海A股**: 600000.SH, 601318.SH, 688001.SH (科创板)
- **深圳A股**: 000001.SZ, 000002.SZ
- **创业板**: 300001.SZ, 300015.SZ

## ⚠️ 注意事项

1. **数据来源**: 基于腾讯财经等免费API，数据仅供参考
2. **使用频率**: 建议合理控制请求频率，避免过于频繁
3. **交易时间**: 实时数据在交易时间内更新最及时
4. **网络依赖**: 需要稳定的网络连接

## 🛠️ 环境要求

- Python 3.6+
- pandas
- requests
- numpy (用于技术指标计算)

```bash
pip install pandas requests numpy
```

## 📝 使用示例

### 获取热门股票行情
```python
from simple_stock_client import SimpleStockClient

# 热门股票
hot_stocks = [
    '000001.SZ',  # 平安银行
    '600519.SH',  # 贵州茅台
    '000858.SZ',  # 五粮液
    '002415.SZ',  # 海康威视
    '600036.SH',  # 招商银行
]

client = SimpleStockClient()
data = client.get_realtime_quote(hot_stocks)

print("热门股票实时行情:")
for code, info in data.items():
    print(f"{code} {info['name']}: {info['current']:.2f} ({info['change_pct']:+.2f}%)")
```

### 技术分析示例
```python
from stock_analyzer import StockAnalyzer

analyzer = StockAnalyzer()

# 分析贵州茅台
analysis = analyzer.analyze_stock('600519.SH')
print(f"趋势: {analysis['technical_analysis']['trend']}")
print(f"RSI: {analysis['technical_analysis']['rsi']:.2f}")
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 免责声明

本工具仅用于学习和研究目的，获取的数据仅供参考，不构成投资建议。投资有风险，入市需谨慎。
