// 股票数据服务
class StockService {
  constructor() {
    this.baseUrl = 'http://localhost:8000'; // 后端API地址
  }

  // 获取实时行情
  async getRealTimeQuotes(stockCodes) {
    try {
      const response = await fetch(`${this.baseUrl}/api/realtime`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stock_codes: stockCodes }),
      });
      
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取实时行情失败:', error);
      // 返回模拟数据
      return this.getMockRealTimeData(stockCodes);
    }
  }

  // 获取历史数据
  async getHistoricalData(stockCode, days = 30) {
    try {
      const response = await fetch(`${this.baseUrl}/api/historical`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stock_code: stockCode, days }),
      });
      
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取历史数据失败:', error);
      // 返回模拟数据
      return this.getMockHistoricalData(stockCode, days);
    }
  }

  // 获取市场指数
  async getMarketIndices() {
    try {
      const response = await fetch(`${this.baseUrl}/api/indices`);
      
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取市场指数失败:', error);
      // 返回模拟数据
      return this.getMockIndicesData();
    }
  }

  // 模拟实时数据
  getMockRealTimeData(stockCodes) {
    const mockData = {};
    const stockNames = {
      '000001.SZ': '平安银行',
      '000002.SZ': '万科A',
      '600000.SH': '浦发银行',
      '600036.SH': '招商银行',
      '600519.SH': '贵州茅台',
      '000858.SZ': '五粮液',
      '002415.SZ': '海康威视',
      '300059.SZ': '东方财富',
    };

    stockCodes.forEach(code => {
      const basePrice = Math.random() * 100 + 10;
      const change = (Math.random() - 0.5) * 10;
      const changePct = (change / basePrice) * 100;
      
      mockData[code] = {
        name: stockNames[code] || '未知股票',
        current: parseFloat((basePrice + change).toFixed(2)),
        pre_close: parseFloat(basePrice.toFixed(2)),
        open: parseFloat((basePrice + Math.random() * 2 - 1).toFixed(2)),
        high: parseFloat((basePrice + Math.abs(change) + Math.random() * 2).toFixed(2)),
        low: parseFloat((basePrice - Math.abs(change) - Math.random() * 2).toFixed(2)),
        volume: Math.floor(Math.random() * 10000000),
        turnover: Math.floor(Math.random() * 1000000000),
        change: parseFloat(change.toFixed(2)),
        change_pct: parseFloat(changePct.toFixed(2)),
        time: new Date().toLocaleTimeString(),
      };
    });

    return mockData;
  }

  // 模拟历史数据
  getMockHistoricalData(stockCode, days) {
    const data = [];
    const basePrice = Math.random() * 100 + 20;
    
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const dayChange = (Math.random() - 0.5) * 5;
      const open = basePrice + dayChange;
      const close = open + (Math.random() - 0.5) * 3;
      const high = Math.max(open, close) + Math.random() * 2;
      const low = Math.min(open, close) - Math.random() * 2;
      
      data.push({
        date: date.toISOString().split('T')[0],
        open: parseFloat(open.toFixed(2)),
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        close: parseFloat(close.toFixed(2)),
        volume: Math.floor(Math.random() * 10000000),
      });
    }
    
    return data;
  }

  // 模拟市场指数数据
  getMockIndicesData() {
    return {
      '上证指数': {
        current: 3424.23,
        change: -24.22,
        change_pct: -0.70,
        volume: 234567890,
      },
      '深证成指': {
        current: 10378.55,
        change: 35.07,
        change_pct: 0.34,
        volume: 345678901,
      },
      '创业板指': {
        current: 2124.34,
        change: 9.91,
        change_pct: 0.47,
        volume: 123456789,
      },
    };
  }

  // 获取热门股票列表
  getPopularStocks() {
    return [
      '000001.SZ', // 平安银行
      '000002.SZ', // 万科A
      '600000.SH', // 浦发银行
      '600036.SH', // 招商银行
      '600519.SH', // 贵州茅台
      '000858.SZ', // 五粮液
      '002415.SZ', // 海康威视
      '300059.SZ', // 东方财富
    ];
  }
}

export default new StockService();
