# -*- coding: utf-8 -*-
"""
xtdata库实时行情和历史数据获取示例
需要先启动MiniQMT客户端
"""

import xtdata
import pandas as pd
import time
from datetime import datetime, <PERSON><PERSON><PERSON>


def connect_to_server():
    """连接到MiniQMT服务器"""
    try:
        print("正在连接MiniQMT服务器...")
        result = xtdata.connect()
        if result:
            print("✓ 连接成功！")
            return True
        else:
            print("✗ 连接失败")
            return False
    except Exception as e:
        print(f"✗ 连接异常: {e}")
        return False


def get_stock_list():
    """获取股票列表示例"""
    try:
        print("\n=== 获取股票列表 ===")
        # 获取沪深A股列表
        stock_list = xtdata.get_stock_list_in_sector('沪深A股')
        print(f"沪深A股总数: {len(stock_list)}")
        print("前10只股票:", stock_list[:10])
        return stock_list[:10]  # 返回前10只用于测试
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']  # 默认股票


def get_realtime_data(stock_codes):
    """获取实时行情数据"""
    try:
        print("\n=== 获取实时行情 ===")
        
        # 获取实时行情
        data = xtdata.get_market_data(
            field_list=['time', 'lastPrice', 'volume', 'turnover', 'openPrice', 'highPrice', 'lowPrice'],
            stock_list=stock_codes,
            period='1m'
        )
        
        if data:
            print("实时行情数据:")
            for stock_code in stock_codes:
                if stock_code in data:
                    stock_data = data[stock_code]
                    print(f"{stock_code}:")
                    print(f"  最新价: {stock_data.get('lastPrice', 'N/A')}")
                    print(f"  成交量: {stock_data.get('volume', 'N/A')}")
                    print(f"  成交额: {stock_data.get('turnover', 'N/A')}")
                    print(f"  开盘价: {stock_data.get('openPrice', 'N/A')}")
                    print(f"  最高价: {stock_data.get('highPrice', 'N/A')}")
                    print(f"  最低价: {stock_data.get('lowPrice', 'N/A')}")
        else:
            print("未获取到实时数据")
            
    except Exception as e:
        print(f"获取实时数据失败: {e}")


def get_historical_data(stock_codes):
    """获取历史数据"""
    try:
        print("\n=== 获取历史数据 ===")
        
        # 设置时间范围（最近30天）
        end_time = datetime.now().strftime('%Y%m%d')
        start_time = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"时间范围: {start_time} 到 {end_time}")
        
        for stock_code in stock_codes[:3]:  # 只获取前3只股票的历史数据
            print(f"\n--- {stock_code} 历史数据 ---")
            
            # 获取日线数据
            data = xtdata.get_market_data_ex(
                field_list=['time', 'open', 'high', 'low', 'close', 'volume'],
                stock_list=[stock_code],
                period='1d',
                start_time=start_time,
                end_time=end_time
            )
            
            if data and stock_code in data:
                df = pd.DataFrame(data[stock_code])
                if not df.empty:
                    print(f"获取到 {len(df)} 条记录")
                    print("最近5天数据:")
                    print(df.tail().to_string())
                else:
                    print("数据为空")
            else:
                print("未获取到历史数据")
                
    except Exception as e:
        print(f"获取历史数据失败: {e}")


def subscribe_realtime_data(stock_codes):
    """订阅实时数据推送"""
    try:
        print("\n=== 订阅实时数据推送 ===")
        
        def on_data(data):
            """数据回调函数"""
            print(f"收到实时数据: {data}")
        
        # 订阅实时数据
        xtdata.subscribe_quote(
            stock_list=stock_codes[:2],  # 只订阅前2只股票
            period='1m',
            callback=on_data
        )
        
        print("已订阅实时数据，等待5秒...")
        time.sleep(5)
        
        # 取消订阅
        xtdata.unsubscribe_quote(stock_codes[:2], '1m')
        print("已取消订阅")
        
    except Exception as e:
        print(f"订阅数据失败: {e}")


def get_market_snapshot(stock_codes):
    """获取市场快照"""
    try:
        print("\n=== 获取市场快照 ===")
        
        # 获取完整的市场快照数据
        snapshot = xtdata.get_full_tick(stock_codes[:3])
        
        if snapshot:
            for stock_code, data in snapshot.items():
                print(f"\n{stock_code} 快照:")
                print(f"  时间: {data.get('time', 'N/A')}")
                print(f"  最新价: {data.get('lastPrice', 'N/A')}")
                print(f"  涨跌幅: {data.get('pctChg', 'N/A')}%")
                print(f"  买一价: {data.get('bidPrice', ['N/A'])[0] if data.get('bidPrice') else 'N/A'}")
                print(f"  卖一价: {data.get('askPrice', ['N/A'])[0] if data.get('askPrice') else 'N/A'}")
        else:
            print("未获取到快照数据")
            
    except Exception as e:
        print(f"获取快照数据失败: {e}")


def main():
    """主函数"""
    print("=" * 50)
    print("xtdata 实时行情和历史数据获取示例")
    print("=" * 50)
    
    # 1. 连接服务器
    if not connect_to_server():
        print("\n请确保:")
        print("1. 已安装并启动MiniQMT客户端")
        print("2. MiniQMT正在运行并监听默认端口")
        return
    
    # 2. 获取股票列表
    stock_codes = get_stock_list()
    
    # 3. 获取实时行情
    get_realtime_data(stock_codes)
    
    # 4. 获取历史数据
    get_historical_data(stock_codes)
    
    # 5. 获取市场快照
    get_market_snapshot(stock_codes)
    
    # 6. 订阅实时数据（可选）
    # subscribe_realtime_data(stock_codes)
    
    print("\n=" * 50)
    print("示例运行完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
